<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generate Test Images</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .canvas-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    button {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
  </style>
</head>
<body>
  <h1>Generate Test Images</h1>
  <p>Click the button below to generate test images for the product gallery.</p>
  <button id="generateBtn">Generate Images</button>
  <div class="canvas-container" id="canvasContainer"></div>

  <script>
    const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#d35400'];
    
    document.getElementById('generateBtn').addEventListener('click', () => {
      const container = document.getElementById('canvasContainer');
      container.innerHTML = '';
      
      for (let i = 1; i <= 8; i++) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 600;
        const ctx = canvas.getContext('2d');
        
        // Заливка фона
        ctx.fillStyle = colors[i - 1];
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Добавление текста
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 30px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`Product ${i}`, canvas.width / 2, canvas.height / 2);
        
        // Добавление на страницу
        container.appendChild(canvas);
        
        // Сохранение изображения
        const link = document.createElement('a');
        link.download = `product${i}.jpg`;
        link.href = canvas.toDataURL('image/jpeg');
        link.click();
      }
      
      alert('All images generated! Please save them to the public/images folder.');
    });
  </script>
</body>
</html>
