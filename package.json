{"name": "lend2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "optimize-videos": "node scripts/optimize-videos.js", "optimize-testimonial-videos": "node scripts/optimize-testimonial-videos.js", "optimize-images": "node scripts/optimize-images.js", "prebuild": "npm run optimize-videos && npm run optimize-testimonial-videos && npm run optimize-images", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "serve": "node server.js"}, "dependencies": {"compression": "^1.8.0", "express": "^5.1.0", "framer-motion": "^12.6.3", "hls.js": "^1.6.2", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "sass": "^1.86.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@google-cloud/storage": "^7.16.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "ffmpeg-static": "^5.2.0", "firebase-tools": "^14.2.2", "globals": "^15.15.0", "imagemin": "^9.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-webp": "^8.0.0", "mime-types": "^3.0.1", "react-metrika": "^0.3.3", "sharp": "^0.34.1", "terser": "^5.39.0", "vite": "^6.2.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-image-presets": "^0.3.4", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-webp-generator": "^0.1.0"}}