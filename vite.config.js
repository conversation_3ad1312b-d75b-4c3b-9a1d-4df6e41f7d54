import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import viteCompression from 'vite-plugin-compression'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => ({
  plugins: [
    react({
      // Оптимизация React для production
      babel: {
        plugins: [
          // Удаление PropTypes в production
          ['transform-react-remove-prop-types', { removeImport: true }]
        ]
      },
      // Более эффективная минификация React компонентов
      jsxRuntime: 'automatic',
    }),
    // Используем стандартный index.html вместо отдельного шаблона
    viteCompression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 10240, // Минимальный размер файла для сжатия (в байтах)
      deleteOriginFile: false, // Сохранять оригинальные файлы
      compressionOptions: { level: 9 }, // Максимальный уровень сжатия
    }),
    viteCompression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 10240,
      deleteOriginFile: false,
      compressionOptions: { level: 11 }, // Максимальный уровень сжатия для brotli
    }),
  ],
  base: './', // Используем относительные пути вместо абсолютных
  server: {
    hmr: {
      overlay: true,
    },
    watch: {
      usePolling: true,
      interval: 100
    },
    fs: {
      strict: false, // Разрешить доступ к файлам вне корневой директории
    },
    // Отключение кэширования в режиме разработки
    headers: {
      'Cache-Control': 'no-store, max-age=0',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
    // Настройка прокси для обхода CORS
    proxy: {
      '/api/mtf-bz': {
        target: 'https://mtforce.mtf.bz',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/mtf-bz/, ''),
        secure: false
      }
    }
  },
  css: {
    devSourcemap: true
  },
  // Настройки оптимизации зависимостей
  optimizeDeps: {
    // Исключаем проблемные зависимости
    exclude: ['react/jsx-runtime']
  },
  build: {
    // Оптимизация изображений при сборке
    assetsInlineLimit: 0, // Не инлайнить ассеты
    minify: 'terser', // Использовать terser для лучшего сжатия
    terserOptions: {
      compress: {
        drop_console: false, // Оставляем console.log для отладки прелоадера
        drop_debugger: true, // Удалять debugger
        pure_funcs: ['console.info', 'console.warn'], // Удалять неиспользуемые функции
        passes: 3, // Несколько проходов оптимизации
        ecma: 2020, // Использовать современный JavaScript
        toplevel: true, // Оптимизировать верхний уровень
        unused: true, // Удалять неиспользуемый код
      },
      mangle: {
        safari10: true, // Совместимость с Safari 10
      },
      format: {
        comments: false, // Удалить все комментарии
        ecma: 2020, // Современный формат вывода
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Более умное разделение на чанки
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('scheduler') || id.includes('prop-types')) {
              return 'vendor-react';
            }
            if (id.includes('framer-motion')) {
              return 'vendor-framer';
            }
            if (id.includes('lucide-react') || id.includes('react-icons')) {
              return 'vendor-icons';
            }
            if (id.includes('react-hook-form') || id.includes('react-metrika')) {
              return 'vendor-forms';
            }

            return 'vendor'; // Все остальные зависимости
          }
          // Разделение компонентов по типам
          if (id.includes('/components/UI/')) {
            return 'ui-components';
          }
        },
        // Оптимизация имен файлов для лучшего кэширования
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      },
      // Удаление неиспользуемых экспортов
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false
      }
    },
    // Включить source maps для production
    sourcemap: false,
    // Оптимизировать размер сборки
    reportCompressedSize: true,
    chunkSizeWarningLimit: 1000
  }
}))
