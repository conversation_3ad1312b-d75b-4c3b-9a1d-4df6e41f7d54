<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/mtforce.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      MTFORCE - Поставщик одежды для организаторов совместных покупок
    </title>

    <!-- Предзагрузка критических ресурсов -->
     <link rel="dns-prefetch" href="//mc.yandex.ru">
    <link
      rel="preload"
      href="/fonts/Unbounded-Regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />
    <link
      rel="preload"
      href="/fonts/Unbounded-Bold.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
    />

    <!-- Стили для прелоадера и основные стили -->
    <style>
      /* Резервируем место для скроллбара сразу, чтобы избежать скачка контента */
      html {
        overflow-y: scroll;
      }

      body {
        margin: 0;
        padding: 0;
        /* Устанавливаем фон, совпадающий с прелоадером, чтобы не было мелькания */
        background-color: #fff;
        /* Устанавливаем базовый шрифт сразу, чтобы избежать FOUT (Flash of Unstyled Text) */
        font-family: "Inter", sans-serif;
      }

      /* Шрифты */

      @font-face {
        font-family: "Unbounded";
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url("/fonts/Unbounded-Regular.woff2") format("woff2");
      }

      @font-face {
        font-family: "Unbounded";
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url("/fonts/Unbounded-Bold.woff2") format("woff2");
      }

      /* Стили для прелоадера */
      #preloader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        /* Анимация исчезновения */
        transition: opacity 0.5s ease-in-out;
        opacity: 1; /* По умолчанию видим */
        visibility: visible; /* По умолчанию видим */
      }

      .preloader-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .preloader-logo {
        width: 180px;
        height: 73px;

      }

      @keyframes logo-pulse {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.05);
          opacity: 0.9;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      .preloader-progress {
        margin-top: 8px;
        width: 160px;
        height: 4px;
        background-color: #ecedee;
        border-radius: 2px;
        overflow: hidden;
        position: relative;
      }

      .preloader-progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 0%;
        background-color: #000000;
        border-radius: 2px;
        animation: progress-animation 2s ease-in-out infinite;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.7);
      }

      @keyframes progress-animation {
        0% {
          width: 0%;
        }
        50% {
          width: 70%;
        }
        100% {
          width: 100%;
        }
      }

      /* Класс для скрытия прелоадера */
      .preloader-hidden {
        opacity: 0;
        visibility: hidden; /* Скрываем полностью после анимации */
        /* Предотвращаем взаимодействие после начала исчезновения */
        pointer-events: none;
      }

      /* Стили для плавного появления React-контента */
      #root {
        opacity: 0;
        transition: opacity 0.5s ease;
      }

      #root.visible {
        opacity: 1;
      }

      /* Стили для SSR-контента */
      #ssr-content {
        opacity: 1;
        transition: opacity 0.5s ease;
      }

      #ssr-content.visible {
        opacity: 1;
      }
    </style>
  </head>
  <body>
    <!-- Прелоадер (временно отключен, см. AppContext.jsx) -->
    <div id="preloader">
      <div class="preloader-content">
        <!-- Встроенный SVG логотип вместо загрузки изображения -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="180"
          height="73"
          fill="none"
        >
          <g clip-path="url(#a)">
            <mask
              id="b"
              width="180"
              height="73"
              x="0"
              y="0"
              maskUnits="userSpaceOnUse"
              style="mask-type: luminance"
            >
              <path fill="#fff" d="M0 0h180v73H0z" />
            </mask>
            <g mask="url(#b)">
              <path
                fill="#000"
                fill-opacity=".8"
                d="M3.725 52.202H0v8.939h3.725zm15.992 8.939V50.454L13.012 61.14H10.53L3.775 50.454H0v-3.845h5.116l6.704 10.037 6.307-10.037h5.314v14.532zm19.885 0h-3.725V50.454h-9.784v-3.845h4.693l14.055.025 4.693-.025v3.845h-9.932zm12.584-10.686v-.226q0-1.498 1.366-2.547 1.366-1.074 3.327-1.074l14.055.026 4.694-.025v3.845zm3.725 10.686h-3.725v-9.039h23.442v3.446H55.91zm22.368-3.62v-7.292q0-1.498 1.366-2.547 1.365-1.074 3.328-1.074l14.054.026q1.962 0 3.328 1.074 1.366 1.049 1.366 2.546v7.266q0 1.499-1.366 2.572-1.365 1.05-3.328 1.049H82.973q-1.962 0-3.328-1.049-1.365-1.074-1.366-2.572m19.742-2.373v-2.796q-.174-.75-1.043-1.324-.87-.573-2.185-.574h-9.635q-1.317 0-2.21.574-.894.575-.944 1.374v2.796q0 .8.894 1.399.893.6 2.235.599h9.784q1.191 0 2.135-.6.969-.624.969-1.448m21.002.4h-10.926v5.593h-3.725v-9.039h19.568v-1.648h-19.568v-3.845h4.694l14.055.025q2.01-.024 3.352.949 1.341.948 1.341 2.872v1.647q0 1.898-1.192 2.672-1.167.774-3.526.774l4.718 5.593h-4.072zm34.884 1.648v3.945h-18.749q-1.96 0-3.327-1.049-1.365-1.074-1.366-2.572v-7.29q0-1.5 1.366-2.548 1.367-1.074 3.327-1.074l14.055.026 4.694-.025v3.845h-16.563q-1.316 0-2.21.574-.894.575-.944 1.374v2.796q0 .8.894 1.399.894.6 2.235.599zm26.093 0v3.945h-18.748q-1.962 0-3.328-1.049-1.365-1.074-1.365-2.572v-5.419H180v3.446h-19.717v1.648zm-23.441-6.742v-.225q0-1.498 1.365-2.547 1.366-1.074 3.328-1.074l14.055.026 4.693-.025v3.845zM96.916 0 64.142 39.1h4.01L96.916 5.643zM82.884 11.792l-22.35 27.31H45L74.666 3.528z"
              />
              <path
                fill="#000"
                fill-opacity=".8"
                d="M96.715 0 131.4 42.132h-4.009L96.715 5.644zM92.44 16.93 73.264 38.997h45.501l-14.819-18.213-4.706 4.553z"
              />
              <path
                fill="#FE0105"
                d="M23.04 69.529v-1.115h1.36l4.076.007q.584-.007.972.275.39.275.389.833v.478q0 .55-.346.774-.338.225-1.022.225H24.12v1.622h-1.08v-2.621h5.674v-.478zm11.814 1.477h-3.168v1.622h-1.08v-2.621h5.674v-.478h-5.674v-1.115h1.36l4.076.007q.584-.007.972.275.39.275.389.833v.478q0 .55-.346.774-.338.225-1.022.225l1.368 1.622h-1.181zm3.318.572v-2.114q0-.435.396-.739.396-.31.964-.31l4.075.006q.57 0 .965.312.396.303.396.738v2.107q0 .435-.396.745-.396.305-.965.305h-4.075q-.568 0-.965-.305-.396-.311-.395-.745m5.723-.688v-.81q-.051-.218-.303-.385a1.13 1.13 0 0 0-.633-.166h-2.794q-.382 0-.64.166-.26.167-.274.399v.81q0 .232.259.406.26.174.648.174h2.837q.345 0 .619-.174.28-.18.281-.42m8.64-.883v1.57q0 .435-.397.746-.396.305-.965.305h-4.075q-.568 0-.965-.305-.396-.311-.396-.745v-2.114q0-.435.396-.739.395-.31.965-.31h5.436v1.114H47.73q-.382 0-.64.166-.26.167-.274.399v.81q0 .232.259.406.261.174.648.174h2.837q.346 0 .583-.145t.267-.348h-3.586v-.984zm5.016.999h-3.168v1.622h-1.08v-2.621h5.673v-.478h-5.673v-1.115h1.36l4.076.007q.584-.007.972.275.389.275.389.833v.478q0 .55-.346.774-.338.225-1.022.225l1.368 1.622h-1.181zm10.114.478v1.144h-5.436q-.569 0-.965-.305-.396-.311-.396-.745v-1.571h6.797v.999h-5.717v.478zm-6.797-1.955v-.065q0-.435.396-.739.396-.31.965-.31l4.075.006 1.361-.007v1.115zm7.566 1.984h5.674v-.478h-4.32q-.62-.015-.98-.26-.36-.247-.374-.74h5.429q.684 0 1.022.225.346.225.346.775v.478q0 .556-.389.84-.389.275-.972.267l-4.075.008h-1.36zm0-1.984h6.797v-1.115l-1.36.007-4.076-.007q-.569 0-.965.311-.396.305-.396.739zM76 71.513h5.673v-.478h-4.32q-.62-.015-.979-.26-.36-.247-.374-.74h5.428q.684 0 1.023.225.345.225.345.775v.478q0 .556-.388.84-.39.275-.972.267l-4.076.008H76zm0-1.984h6.796v-1.115l-1.36.007-4.076-.007q-.568 0-.964.311-.396.305-.396.739zm8.645-1.115v1.115h-1.08v-1.115zm0 4.214h-1.08v-2.636h1.08zm1.864-4.214 2.31 3.099 2.326-3.099h1.08l-2.85 4.214H88.28l-2.851-4.214zm13.282 3.07v1.144h-5.436q-.57 0-.965-.305-.396-.311-.396-.745v-1.571h6.797v.999h-5.717v.478zm-6.797-1.955v-.065q0-.435.396-.739.396-.31.965-.31l4.075.006 1.36-.007v1.115zm11.883 2.049v-2.114q0-.435.396-.739.396-.31.965-.31l4.075.006q.569 0 .965.312.396.303.396.738v2.107q0 .435-.396.745-.396.305-.965.305h-4.075q-.569 0-.965-.305-.396-.311-.396-.745m5.724-.688v-.81q-.05-.218-.302-.385a1.13 1.13 0 0 0-.634-.166h-2.794q-.382 0-.64.166-.26.167-.274.399v.81q0 .232.259.406.26.174.648.174h2.837q.346 0 .619-.174.28-.18.281-.42m7.565 0v-.81l-.007-1.666h1.08v3.164q0 .435-.396.745-.395.305-.965.305h-4.075q-.569 0-.965-.305-.396-.311-.396-.745v-3.164h1.08v2.49q0 .232.26.406.258.174.648.174h2.836q.346 0 .62-.174.28-.18.28-.42m5.759 1.738h-1.08v-3.099h-2.837v-1.115h1.361l4.075.007 1.361-.007v1.115h-2.88zm9.373-1.738v-.81q-.051-.218-.303-.385a1.13 1.13 0 0 0-.633-.166h-4.788v-1.115h1.361l4.075.007q.569 0 .965.312.396.303.396.738v2.107q0 .435-.396.745-.396.305-.965.305h-5.436v-2.636h1.08v1.492h3.744q.345 0 .619-.174.28-.18.281-.42m1.841.688v-2.114q0-.435.396-.739.396-.31.965-.31l4.075.006q.569 0 .965.312.396.303.396.738v2.107q0 .435-.396.745-.396.305-.965.305H136.5q-.569 0-.965-.305-.396-.311-.396-.745m5.724-.688v-.81q-.05-.218-.302-.385-.252-.165-.634-.166h-2.793q-.382 0-.641.166-.26.167-.274.399v.81q0 .232.259.406.26.174.648.174h2.837q.346 0 .619-.174.281-.18.281-.42m1.842.688v-2.114q0-.435.396-.739.396-.31.965-.31l4.075.006q.569 0 .965.312.396.303.396.738v2.107q0 .435-.396.745-.396.305-.965.305h-4.075q-.569 0-.965-.305-.396-.311-.396-.745m5.724-.688v-.81q-.05-.218-.302-.385-.252-.165-.634-.166H144.7q-.382 0-.641.166-.26.167-.274.399v.81q0 .232.259.406.26.174.648.174h2.837q.346 0 .619-.174.281-.18.281-.42m6.09.116h-3.168v1.622h-1.08v-2.621h5.673v-.478h-5.673v-1.115h1.36l4.076.007q.582-.007.972.275.388.275.388.833v.478q0 .55-.345.774-.339.225-1.023.225l1.368 1.622h-1.18z"
              />
            </g>
          </g>
          <defs>
            <clipPath id="a"><path fill="#fff" d="M0 0h180v73H0z" /></clipPath>
          </defs>
        </svg>

        <div class="preloader-progress">
          <div class="preloader-progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- Скрипт для управления viewport на маленьких экранах -->
    <script>
      (function() {
        var minWidth = 360;

        function updateViewport() {
          var viewportMeta = document.querySelector('meta[name="viewport"]');
          if (!viewportMeta) {
            viewportMeta = document.createElement('meta');
            viewportMeta.setAttribute('name', 'viewport');
            document.head.appendChild(viewportMeta);
          }

          var screenWidth = window.innerWidth;

          if (screenWidth < minWidth) {
            // Устанавливаем фиксированную ширину viewport и масштабирование
            viewportMeta.setAttribute('content', 'width=' + minWidth + ', initial-scale=' + (screenWidth/minWidth) + ', maximum-scale=' + (screenWidth/minWidth) + ', user-scalable=no');
          } else {
            // Иначе используем стандартный адаптивный viewport
            viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=5.0');
          }
        }

        // Обновляем viewport при загрузке страницы
        updateViewport();

        // Обновляем viewport при изменении размера окна
        window.addEventListener('resize', updateViewport);
      })();
    </script>

    <!-- Корневой элемент для React-приложения -->
    <div id="root"></div>

    <!-- Основной скрипт приложения -->
    <script type="module" src="/src/main.jsx"></script>

    <!-- Begin LeadBack code - отложенная загрузка для оптимизации производительности -->
    <script>
      // Определяем глобальную переменную _emv сразу
      var _emv = _emv || [];
      _emv['campaign'] = '019f8b946a256d13bd637ad9';

      // Отложенная инициализация LeadBack после загрузки критических ресурсов
      function initLeadBack() {
        (function() {
          var em = document.createElement('script');
          em.type = 'text/javascript';
          em.async = true;
          em.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'leadback.ru/js/leadback.js';
          var s = document.getElementsByTagName('script')[0];
          s.parentNode.insertBefore(em, s);
        })();
      }

      // Инициализируем LeadBack после загрузки DOM или через 3 секунды (что наступит раньше)
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
          setTimeout(initLeadBack, 3000); // Дополнительная задержка 3 секунды после DOMContentLoaded
        });
      } else {
        setTimeout(initLeadBack, 3000); // Если DOM уже загружен, ждем 3 секунды
      }
    </script>
    <!-- End LeadBack code -->

  </body>
</html>
