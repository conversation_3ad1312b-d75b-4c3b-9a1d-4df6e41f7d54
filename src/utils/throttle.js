/**
 * Создает функцию, которая не будет вызываться чаще, чем раз в указанный период времени
 * @param {Function} func - Функция для тротлинга
 * @param {number} limit - Минимальный интервал между вызовами в миллисекундах
 * @returns {Function} - Тротлированная функция
 */
export function throttle(func, limit) {
  let inThrottle;
  let lastFunc;
  let lastRan;
  
  return function() {
    const context = this;
    const args = arguments;
    
    if (!inThrottle) {
      func.apply(context, args);
      lastRan = Date.now();
      inThrottle = true;
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(function() {
        if (Date.now() - lastRan >= limit) {
          func.apply(context, args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
}
