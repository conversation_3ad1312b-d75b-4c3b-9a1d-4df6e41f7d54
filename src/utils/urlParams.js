/**
 * Утилиты для работы с параметрами URL
 */

/**
 * Получает значение параметра из URL
 * @param {string} paramName - Имя параметра
 * @returns {string|null} Значение параметра или null, если параметр не найден
 */
export const getUrlParam = (paramName) => {
  const params = new URLSearchParams(window.location.search);
  return params.get(paramName);
};

/**
 * Получает все UTM-метки из URL
 * @returns {Object} Объект с UTM-метками
 */
export const getUtmParams = () => {
  const params = new URLSearchParams(window.location.search);
  const utmParams = {
    utm_source: params.get('utm_source') || '',
    utm_medium: params.get('utm_medium') || '',
    utm_campaign: params.get('utm_campaign') || '',
    utm_content: params.get('utm_content') || '',
    utm_term: params.get('utm_term') || '',
    _ym_uid: params.get('_ym_uid') || '' // Яндекс Метрика удалена
  };

  // Сохраняем UTM-метки в localStorage для использования на других страницах
  saveUtmParamsToLocalStorage(utmParams);

  return utmParams;
};

/**
 * Сохраняет UTM-метки в localStorage
 * @param {Object} utmParams - Объект с UTM-метками
 */
const saveUtmParamsToLocalStorage = (utmParams) => {
  // Сохраняем только если есть хотя бы одна непустая UTM-метка
  const hasUtmParams = Object.values(utmParams).some(value => value);

  if (hasUtmParams) {
    localStorage.setItem('utm_params', JSON.stringify(utmParams));
  }
};

/**
 * Получает UTM-метки из localStorage, если они есть
 * @returns {Object} Объект с UTM-метками
 */
export const getUtmParamsFromLocalStorage = () => {
  const storedParams = localStorage.getItem('utm_params');
  return storedParams ? JSON.parse(storedParams) : {
    utm_source: '',
    utm_medium: '',
    utm_campaign: '',
    utm_content: '',
    utm_term: '',
    _ym_uid: ''
  };
};
