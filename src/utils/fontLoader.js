/**
 * Утилита для управления загрузкой шрифтов
 * Использует Font Loading API для контроля загрузки шрифтов
 * и предотвращения "прыжков" текста
 */

// Проверяем поддержку Font Loading API
const fontLoadingSupported = typeof document !== 'undefined' && 'fonts' in document;

/**
 * Загружает все необходимые шрифты
 * @returns {Promise} Promise, который разрешается, когда все шрифты загружены
 */
export const loadFonts = () => {
  if (!fontLoadingSupported) {
    console.warn('Font Loading API не поддерживается в этом браузере');
    return Promise.resolve();
  }

  // Список шрифтов для загрузки (Manrope удален)
  const fontFamilies = [
    { family: 'Unbounded', weight: 400 },
    { family: 'Unbounded', weight: 700 },
    { family: 'Inter', weight: 400 },
    { family: 'Inter', weight: 500 }
  ];

  // Создаем промисы для загрузки каждого шрифта
  const fontPromises = fontFamilies.map(font => {
    return document.fonts.load(`${font.weight} 1em "${font.family}"`);
  });

  // Возвращаем промис, который разрешается, когда все шрифты загружены
  return Promise.all(fontPromises)
    .then(() => {
      console.log('Все шрифты успешно загружены');
      return true;
    })
    .catch(err => {
      console.error('Ошибка при загрузке шрифтов:', err);
      return false;
    });
};

/**
 * Проверяет, загружены ли шрифты из кеша
 * @returns {boolean} true, если все шрифты уже загружены
 */
export const checkCachedFonts = () => {
  if (!fontLoadingSupported) {
    return false;
  }

  // Проверяем основные шрифты (Manrope удален)
  return document.fonts.check('400 1em "Unbounded"') &&
         document.fonts.check('400 1em "Inter"');
};

/**
 * Загружает критические ресурсы (шрифты и изображения)
 * @returns {Promise} Promise, который разрешается, когда все ресурсы загружены
 */
export const loadCriticalResources = () => {
  // Загружаем шрифты
  const fontsPromise = loadFonts();

  // Проверяем, является ли устройство мобильным
  const isMobile = window.innerWidth < 768;

  // Массив для хранения промисов загрузки изображений
  const imagePromises = [];

  // Загружаем первое изображение слайдера только на десктопах
  if (!isMobile) {
    // Предзагружаем как WebP, так и JPG версии для поддержки всех браузеров
    const criticalImages = [
      '/images/slider1.webp',
      '/images/slider1.jpg'
    ];

    criticalImages.forEach(src => {
      imagePromises.push(
        new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(src);
          img.onerror = () => {
            console.warn(`Не удалось загрузить изображение: ${src}`);
            resolve(null); // Разрешаем промис даже при ошибке, чтобы не блокировать загрузку
          };
          img.src = src;
        })
      );
    });
  }

  // Возвращаем промис, который разрешается, когда все ресурсы загружены
  return Promise.all([fontsPromise, ...imagePromises])
    .then(() => {
      console.log('Все критические ресурсы загружены');
      return true;
    })
    .catch(err => {
      console.error('Ошибка при загрузке критических ресурсов:', err);
      return false;
    });
};
