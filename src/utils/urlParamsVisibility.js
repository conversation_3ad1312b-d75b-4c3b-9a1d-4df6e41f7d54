/**
 * Утилиты для управления видимостью элементов в зависимости от URL-параметров
 */
import { getUrlParam } from './urlParams';

/**
 * Применяет стили видимости в зависимости от URL-параметров
 * - Если prices=partners, скрываем .product-price-wrapper
 * - Если catalog=partnersnj, скрываем .product-gallery
 */
export const applyUrlParamsVisibility = () => {
  // Получаем параметры из URL
  const pricesParam = getUrlParam('prices');
  const catalogParam = getUrlParam('catalog');

  // Создаем стиль, если его еще нет
  let styleElement = document.getElementById('url-params-visibility-styles');
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'url-params-visibility-styles';
    document.head.appendChild(styleElement);
  }

  // Формируем CSS правила
  let cssRules = '';

  // Если prices=partners, скрываем .product-price-wrapper
  if (pricesParam === 'partners') {
    cssRules += '.product-price-wrapper { display: none !important; }';
  }

  // Если catalog=partnersnj, скрываем .product-gallery
  if (catalogParam === 'partnersnj') {
    cssRules += '.product-gallery { display: none !important; }';
  }

  // Применяем стили
  styleElement.textContent = cssRules;
};
