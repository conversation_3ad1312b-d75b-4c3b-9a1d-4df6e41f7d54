/**
 * Пустые функции аналитики (Яндекс.Метрика удалена)
 *
 * Эти функции оставлены для совместимости с существующим кодом,
 * но не выполняют никаких действий по отправке аналитики.
 */

/**
 * Инициализация аналитики (пустая функция)
 */
export const initYandexMetrika = () => {
  // Функция оставлена для совместимости
  console.log('Analytics initialization skipped (analytics removed)');
};

/**
 * Отправка цели (пустая функция)
 * @param {string} goalName - Имя цели
 */
export const sendYandexMetrikaGoal = (goalName) => {
  // Функция оставлена для совместимости
  console.log(`Analytics event skipped: ${goalName}`);
};

/**
 * Отправка события лида (пустая функция)
 * @param {string} type - Тип лида: 'default', 'quiz', 'catalog'
 */
export const sendLeadEvent = (type = 'default') => {
  // Функция оставлена для совместимости
  console.log(`Lead event skipped: ${type}`);
};
