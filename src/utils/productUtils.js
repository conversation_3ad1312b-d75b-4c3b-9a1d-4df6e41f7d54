/**
 * Утилиты для работы с данными продуктов
 */

/**
 * Форматирование цены
 */
export const formatPrice = (price) => {
  return new Intl.NumberFormat('ru-RU').format(price) + ' ₽';
};

/**
 * Разделение названия на две строки
 */
export const splitNameIntoTwoLines = (name) => {
  const words = name.split(' ');

  if (words.length === 1) {
    // Если название состоит из одного слова, разделяем его пополам
    const halfLength = Math.ceil(name.length / 2);
    return [name.substring(0, halfLength), name.substring(halfLength)];
  } else if (words.length === 2) {
    // Если название состоит из двух слов, каждое слово на отдельной строке
    return [words[0], words[1]];
  } else {
    // Если больше двух слов, балансируем строки
    let firstLine = '';
    let secondLine = '';
    const totalLength = name.length;
    let currentLength = 0;

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      currentLength += word.length;

      if (currentLength < totalLength / 2) {
        firstLine += (firstLine ? ' ' : '') + word;
      } else {
        secondLine += (secondLine ? ' ' : '') + word;
      }
    }

    return [firstLine, secondLine];
  }
};
