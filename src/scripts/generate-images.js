const fs = require('fs');
const { createCanvas } = require('canvas');

// Создаем директорию для изображений, если она не существует
if (!fs.existsSync('public/images')) {
  fs.mkdirSync('public/images', { recursive: true });
}

// Функция для создания тестового изображения
function createTestImage(filename, width, height, color) {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  
  // Заливка фона
  ctx.fillStyle = color;
  ctx.fillRect(0, 0, width, height);
  
  // Добавление текста
  ctx.fillStyle = '#ffffff';
  ctx.font = 'bold 30px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(filename.replace('.jpg', ''), width / 2, height / 2);
  
  // Сохранение изображения
  const buffer = canvas.toBuffer('image/jpeg');
  fs.writeFileSync(`public/images/${filename}`, buffer);
  
  console.log(`Created image: ${filename}`);
}

// Создаем тестовые изображения
const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#d35400'];

for (let i = 1; i <= 8; i++) {
  createTestImage(`product${i}.jpg`, 400, 600, colors[i - 1]);
}

console.log('All test images created successfully!');
