/**
 * Стили для скелетонов (заглушек) при прогрессивной загрузке
 */

// Базовые стили для всех скелетонов
.section-skeleton {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
  
  // Анимация пульсации для скелетонов
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
      rgba(255, 255, 255, 0) 0%, 
      rgba(255, 255, 255, 0.2) 50%, 
      rgba(255, 255, 255, 0) 100%);
    animation: skeleton-pulse 1.5s infinite;
  }
  
  // Заголовок скелетона
  .skeleton-title {
    height: 32px;
    width: 60%;
    background-color: rgba(0, 0, 0, 0.05);
    margin: 2rem auto 1.5rem;
    border-radius: 0.5rem;
  }
  
  // Элементы скелетона
  .skeleton-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 0 1rem;
    
    &::before, &::after {
      content: '';
      display: block;
      height: 100px;
      margin: 1rem;
      border-radius: 0.5rem;
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    &::before {
      width: calc(50% - 2rem);
    }
    
    &::after {
      width: calc(50% - 2rem);
    }
  }
}

// Специфические стили для разных типов скелетонов
.benefits-skeleton {
  .skeleton-items {
    &::before, &::after {
      height: 80px;
    }
  }
}

.gallery-skeleton {
  .skeleton-items {
    &::before, &::after {
      height: 200px;
    }
  }
}

.footer-skeleton {
  margin-bottom: 0;
}

// Анимация пульсации
@keyframes skeleton-pulse {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Адаптивные стили для мобильных устройств
@media (max-width: 768px) {
  .section-skeleton {
    .skeleton-title {
      width: 80%;
    }
    
    .skeleton-items {
      &::before, &::after {
        width: 100%;
        margin: 0.5rem 1rem;
      }
    }
  }
}
