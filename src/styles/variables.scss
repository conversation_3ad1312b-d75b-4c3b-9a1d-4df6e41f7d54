// Modern 2025 Color Palette - Professional & Clean
$white: #fff;
$black: #000;

// Base colors
$slate-ultralight: #fafafb;   // Base background color
$slate: #3e4752;
$slate-lighter: #f1f5f9;         // Main text color
$slate-light: #ecedee;        // Light background elements
$slate-dark: #2c333b;         // Darker accents
$dark-gray: #444444;          // Темно-серый для кнопок

// Accent colors
$blue: #0047ff;              // Primary accent - vibrant blue
$blue-dark: #0035c8;          // Darker blue for hover states
$blue-light: #e6ecff;         // Light blue for backgrounds

// Functional colors
$red: #ff0000;                // Error/important notifications
$green: #00b67a;              // Success indicators
$yellow: #ffb400;             // Warnings/highlights
$orange: #ff7a00;             // Orange accent
$pink: #ff2d92;               // Pink accent

// Functional color assignments
$primary-color: $red;         // Primary action color (buttons)
$primary-light: $slate-light;
$primary-dark:  $slate; // Lighter version for hover states
$secondary-color: $slate;     // Secondary actions
$accent-color: $blue;         // Accent elements (text highlights, icons)
$text-color: $slate;          // Main text
$light-text: $white;          // Text on dark backgrounds
$dark-text: $slate-dark;      // Headings and important text
$background-light: $white;    // Main background
$background-dark: $slate-ultralight; // Secondary background
$border-color: $slate-light;  // Subtle borders
$error-color: $red;           // Error messages
$success-color: $green;       // Success messages

// Additional colors for specific components
$header-bg: $slate-ultralight; // Clean light header
$hero-bg: $slate-ultralight;   // Light background for hero
$card-bg: $white;              // Card background
$light-bg: $slate-ultralight;  // Light background for form elements
$rating-text: $slate;          // Rating text color
$subtle-bg: $slate-ultralight; // Subtle background for cards
$button-shadow: rgba($blue, 0.15); // Shadow for buttons

// Typography - Optimized for Cyrillic
$font-family-main: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
$font-family-heading: 'Unbounded', 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
$font-family-accent: 'Manrope', 'Arial', sans-serif;

// Font sizes - More dramatic scale for 2025
$font-size-xs: 0.75rem;     // 12px
$font-size-sm: 0.875rem;    // 14px
$font-size-base: 1rem;      // 16px
$font-size-md: 1.125rem;    // 18px
$font-size-lg: 1.375rem;    // 22px - slightly larger
$font-size-xl: 1.75rem;     // 28px - more impact
$font-size-2xl: 2.25rem;    // 36px - bolder headlines
$font-size-3xl: 3rem;       // 48px - stronger presence
$font-size-4xl: 3.75rem;    // 60px - dramatic headlines
$font-size-5xl: 4.5rem;     // 72px - for hero sections

// Spacing
$spacing-xs: 0.25rem;      // 4px
$spacing-sm: 0.5rem;       // 8px
$spacing-md: 1rem;         // 16px
$spacing-lg: 1.5rem;       // 24px
$spacing-xl: 2rem;         // 32px
$spacing-2xl: 3rem;        // 48px
$spacing-3xl: 4rem;        // 64px

// Breakpoints
$breakpoint-xs: 0;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1400px;

// Border radius - 2025 style with more varied options
$border-radius-xs: 4px;
$border-radius-sm: 8px;
$border-radius-md: 12px;
$border-radius-lg: 20px;
$border-radius-xl: 28px;
$border-radius-2xl: 36px;
$border-radius-full: 9999px;

// Shadows - 2025 style with softer, more modern feel
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.12);
$shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05);
$shadow-lg: 0 12px 32px rgba(0, 0, 0, 0.08), 0 6px 16px rgba(0, 0, 0, 0.04);
$shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.1), 0 8px 20px rgba(0, 0, 0, 0.05);
$shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-colored: 0 8px 24px rgba(37, 99, 235, 0.15);  // Primary color shadow

// Transitions
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
