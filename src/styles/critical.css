/* Critical CSS для первого экрана */
:root {
  --white: #fff;
  --black: #000;
  --seashell: #eff2f1;
  --slate: #3e4752;
  --slate-transparent: #c5c8cb;
  --slate-transparent-light: #ecedee;
  --slate-transparent-ultralight: #fafafb;
  --red: #ff003d;
  
  --font-family-primary: 'Inter', sans-serif;
  --font-family-secondary: 'Manrope', sans-serif;
  --font-family-accent: 'Unbounded', sans-serif;
  
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-xs: 0.75rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.75rem;
  --font-size-2xl: 2.25rem;
  --font-size-3xl: 3rem;
  
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-xl: 1.5rem;
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05);
  
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

body {
  margin: 0;
  font-family: var(--font-family-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--slate);
  background-color: var(--slate-transparent-ultralight);
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
  transition: background-color var(--transition-normal);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 40px;
  width: auto;
}

.hero-section {
  position: relative;
  padding-top: 80px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background-color: var(--slate-transparent-ultralight);
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  margin-bottom: 2rem;
}

.hero-title {
  font-family: var(--font-family-accent);
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--slate);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: 2rem;
  color: var(--slate);
}

@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-base);
  }
}
