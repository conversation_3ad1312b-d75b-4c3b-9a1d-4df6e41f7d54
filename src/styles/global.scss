@use 'variables' as *;
@use 'sass:color';
@import 'skeletons';

// Стили для управления загрузкой контента
.content-hidden {
  visibility: hidden;
  opacity: 0;
  pointer-events: none; // Отключаем взаимодействие с элементами
}

.content-visible {
  visibility: visible;
  opacity: 1;
  transition: opacity 0.8s ease-in-out; // Увеличиваем время анимации для более плавного появления
}

// Стили для инлайнового прелоадера (дополнительные стили)
#inline-preloader {
  transition: opacity 0.5s ease-in-out;
}

*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: $font-family-main;
  color: $text-color;
  background-color: $background-light;
  line-height: 1.6;
  overflow-x: hidden;
  font-feature-settings: 'ss01', 'ss03', 'cv01', 'cv02';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: font-family 0.3s ease-in-out;
}

h1, h2, h3, h4, h5, h6 {
  font-family: $font-family-heading;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: $spacing-md;
  letter-spacing: 0;
  color: $dark-text;
  transition: font-family 0.3s ease-in-out;
}

h1 {
  font-size: $font-size-3xl;
  font-weight: 800;

  @media (min-width: $breakpoint-md) {
    font-size: $font-size-4xl;
  }

  @media (min-width: $breakpoint-lg) {
    font-size: $font-size-5xl;
  }
}

h2 {
  font-size: $font-size-2xl;
  position: relative;

  @media (min-width: $breakpoint-md) {
    font-size: $font-size-3xl;
  }

  &::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background-color: $blue;
    margin-top: $spacing-sm;
    border-radius: $border-radius-full;
    margin-left: auto;
    margin-right: auto;
  }

  &.text-center::after {
    margin-left: auto;
    margin-right: auto;
  }
}

h3 {
  font-size: $font-size-xl;

  @media (min-width: $breakpoint-md) {
    font-size: $font-size-2xl;
  }
}

p {
  margin-bottom: $spacing-md;
}

a {
  color: $primary-color;
  text-decoration: none;
  transition: all $transition-normal;
  font-weight: 500;

  &:hover {
    color: color.scale($blue, $lightness: -20%);
    text-decoration: underline;
  }
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
  font-family: $font-family-main;
}

input, textarea, select {
  font-family: $font-family-main;
  font-size: $font-size-base;
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 $spacing-md;


  @media (min-width: $breakpoint-md) {
    padding: 0 $spacing-lg;
  }

  @media (min-width: $breakpoint-lg) {
    padding: 0 $spacing-xl;
  }
}

.section {
padding: $spacing-2xl 0;
  position: relative;
  overflow: hidden;

  @media (min-width: $breakpoint-lg) {
    padding: 3rem 0;
  }

  &-title {
      font-size:2rem;
    margin-bottom: $spacing-lg;
    position: relative;
    z-index: 1;



    @media (min-width: $breakpoint-lg) {
      font-size: $font-size-3xl;
    }
  }

  &-subtitle {
    font-size: $font-size-md;
    color: $text-color;
    margin-top: -$spacing-md;
    margin-bottom: $spacing-lg;
    max-width: 800px;
    font-family: $font-family-main;

    @media (min-width: $breakpoint-md) {
      font-size: $font-size-lg;
    }
  }

  &-header {
    margin-bottom: $spacing-2xl;
  }
}

.text-center {
  text-align: center;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $secondary-color;
}

.text-accent {
  color: $accent-color;
}

.bg-light {
  background-color: $background-light;
}

.bg-dark {
  background-color: $background-dark;
}

.hidden {
  display: none;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: $spacing-sm;
}

.gap-md {
  gap: $spacing-md;
}

.gap-lg {
  gap: $spacing-lg;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.rounded {
  border-radius: $border-radius-md;
}

.shadow {
  box-shadow: $shadow-md;
}

.transition {
  transition: all $transition-normal;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.5s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.badge {
  display: inline-block;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-full;
  font-size: $font-size-xs;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.badge-primary {
    background-color: $primary-color;
    color: $light-text;
  }

  &.badge-secondary {
    background-color: $secondary-color;
    color: $light-text;
  }

  &.badge-accent {
    background-color: $accent-color;
    color: $light-text;
  }

  &.badge-outline {
    background-color: transparent;
    border: 1px solid currentColor;
  }
}
.mobile-only{
  @media (min-width: $breakpoint-lg) {
    display: none;
  }
}
.desktop-only{
  @media (max-width: $breakpoint-lg) {
    display: none;
  }
}