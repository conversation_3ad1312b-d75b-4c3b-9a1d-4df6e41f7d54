import { createContext, useContext, useState, useEffect } from 'react';
import { loadCriticalResources } from '../utils/fontLoader';

// Создаем контекст приложения
const AppContext = createContext();

// Хук для использования контекста
export const useAppContext = () => useContext(AppContext);

// Провайдер контекста
export const AppProvider = ({ children }) => {
  // Состояние загрузки приложения
  const [isLoading, setIsLoading] = useState({
    critical: true,    // Критические ресурсы (шапка, основные стили)
    secondary: true,   // Вторичные ресурсы (первый экран контента)
    complete: true     // Полная загрузка всех ресурсов
  });

  // Состояние для отслеживания, был ли это первый визит
  const [isFirstVisit, setIsFirstVisit] = useState(true);

  // Состояние для отслеживания ошибок загрузки
  const [loadingError, setLoadingError] = useState(null);

  // Эффект для инициализации приложения и прогрессивной загрузки
  useEffect(() => {
    // Проверяем, был ли уже визит на сайт
    const hasVisited = localStorage.getItem('hasVisited') === 'true';
    setIsFirstVisit(!hasVisited);

    // Функция для загрузки ресурсов
    const initializeApp = async () => {
      try {
        // Этап 1: Загрузка критических ресурсов (шапка, основные стили)
        console.log('Начинаем загрузку критических ресурсов...');

        // Устанавливаем таймаут для максимального времени ожидания критических ресурсов
        // В production режиме это должно происходить быстрее
        console.log('Устанавливаем таймаут для критических ресурсов');

        // Немедленно отмечаем, что критические ресурсы загружены
        // Это позволит быстро скрыть прелоадер и показать контент
        setIsLoading(prev => ({ ...prev, critical: false }));

        // Загружаем критические ресурсы
        loadCriticalResources()
          .then(() => {
            console.log('Критические ресурсы успешно загружены');
            // Критические ресурсы уже отмечены как загруженные выше

            // Отмечаем, что пользователь посетил сайт
            localStorage.setItem('hasVisited', 'true');

            // Этап 2: Загрузка вторичных ресурсов (первый экран контента)
            setTimeout(() => {
              console.log('Вторичные ресурсы загружены');
              setIsLoading(prev => ({ ...prev, secondary: false }));

              // Этап 3: Полная загрузка всех ресурсов
              setTimeout(() => {
                console.log('Все ресурсы загружены');
                setIsLoading(prev => ({ ...prev, complete: false }));
              }, 2000);
            }, 2000);
          })
          .catch(error => {
            console.error('Ошибка при загрузке критических ресурсов:', error);
            // Критические ресурсы уже отмечены как загруженные выше
            setLoadingError(error.message || 'Ошибка при загрузке ресурсов');
          });
      } catch (error) {
        console.error('Ошибка при инициализации приложения:', error);
        setLoadingError(error.message || 'Произошла ошибка при загрузке приложения');
        setIsLoading({ critical: false, secondary: false, complete: false });
      }
    };

    // Запускаем инициализацию
    initializeApp();
  }, []);

  // Значение контекста
  const contextValue = {
    isLoading,
    isFirstVisit,
    loadingError
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContext;
