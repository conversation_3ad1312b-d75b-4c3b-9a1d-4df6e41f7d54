import { useState, Suspense, lazy, useEffect } from 'react'
import './styles/global.scss'
import './styles/skeleton.scss'
import './components/HeroHeader.scss'

// Импортируем утилиты URL-параметров
import { applyUrlParamsVisibility } from './utils/urlParamsVisibility'

// Импортируем контекст приложения
import { AppProvider, useAppContext } from './contexts/AppContext'

// Импортируем критические компоненты (загружаются сразу)
import MetrikaInit from './components/MetrikaInit'
import Preloader from './components/Preloader'
import Header from './components/Header'
import HeroSection from './components/HeroSection'
import MobileMenu from './components/MobileMenu'

// Компоненты второго приоритета (загружаются после критических)
import GiftButton from './components/UI/GiftButton'
import QuizModal from './components/UI/QuizModal'
import CountdownTimer from './components/UI/CountdownTimer'

// Некритические компоненты (загружаются отложенно)
// Приоритет 1: Компоненты, видимые сразу после скролла
const BenefitsSection = lazy(() => import('./components/BenefitsSection'))
const ProductGallery = lazy(() => import('./components/ProductGallery'))

// Приоритет 2: Компоненты, видимые при дальнейшем скролле
const PartnershipBanner = lazy(() => import('./components/PartnershipBanner'))
const PartnershipSteps = lazy(() => import('./components/PartnershipSteps'))
const Testimonials = lazy(() => import('./components/Testimonials'))
const FAQ = lazy(() => import('./components/FAQ'))
const Footer = lazy(() => import('./components/Footer'))

// Внутренний компонент App, который использует контекст
function AppContent() {
  const [isQuizModalOpen, setIsQuizModalOpen] = useState(false);
  const [showFullQuiz, setShowFullQuiz] = useState(true);

  // Получаем функции управления таймером из контекста
  const { isStickyTimerVisible, hideStickyTimer, showStickyTimer } = useAppContext();

  // Применение видимости по URL-параметрам
  useEffect(() => {
    // Применяем стили видимости в зависимости от URL-параметров
    applyUrlParamsVisibility();
  }, []); // Пустой массив зависимостей означает, что эффект выполнится один раз после первого рендера

  const openQuizModal = (fullQuiz = true) => {
    // Если fullQuiz = true, показываем полный квиз с нулевым этапом
    // Если fullQuiz = false, показываем только финальную форму
    setShowFullQuiz(fullQuiz);
    setIsQuizModalOpen(true);
    // Скрываем прилипающий таймер при открытии модального окна
    hideStickyTimer();
  };

  // Добавляем функцию в глобальный объект window для доступа из других компонентов
  useEffect(() => {
    window.openQuizModal = openQuizModal;
    return () => {
      delete window.openQuizModal;
    };
  }, []);

  const closeQuizModal = () => {
    setIsQuizModalOpen(false);
    // Показываем прилипающий таймер при закрытии модального окна
    showStickyTimer();
  };

  return (
    <>
      {/* Инициализация Яндекс Метрики в самом начале приложения */}
      <MetrikaInit />

      {/* Компонент для управления прелоадером */}
      <Preloader />

      {/* Прилипающий таймер для десктопа - управляется через контекст */}
      <div className={`sticky-countdown-container ${isStickyTimerVisible ? 'visible' : 'hidden'}`}>
        <div className="container">
          <CountdownTimer targetDate="2025-11-30T23:59:59" className="sticky-countdown" />
        </div>
      </div>

      <div className="app">
        {/* Критические компоненты (загружаются сразу) */}
        <div className="hero-header">
          <div className="decorative-element"></div>
          <Header />
          <HeroSection openQuizModal={() => openQuizModal(false)} />
        </div>

        <main>
          {/* Компоненты первого приоритета (загружаются сразу после критических) */}
          <Suspense fallback={
            <div className="section-skeleton benefits-skeleton" style={{ height: '300px', background: '#fafafb' }}>
              <div className="skeleton-title"></div>
              <div className="skeleton-items"></div>
            </div>
          }>
            <BenefitsSection />
          </Suspense>

          <Suspense fallback={
            <div className="section-skeleton gallery-skeleton" style={{ height: '400px', background: '#fafafb' }}>
              <div className="skeleton-title"></div>
              <div className="skeleton-items"></div>
            </div>
          }>
            <ProductGallery />
          </Suspense>

          {/* Компоненты второго приоритета (загружаются в фоне) */}
          <Suspense fallback={<div className="section-skeleton" style={{ height: '300px', background: '#f5f5f7' }}></div>}>
            <PartnershipBanner />
          </Suspense>

          <Suspense fallback={<div className="section-skeleton" style={{ height: '400px', background: '#fafafb' }}></div>}>
            <Testimonials />
          </Suspense>

          <Suspense fallback={<div className="section-skeleton" style={{ height: '400px', background: '#f5f5f7' }}></div>}>
            <PartnershipSteps />
          </Suspense>

          <Suspense fallback={<div className="section-skeleton" style={{ height: '300px', background: '#fafafb' }}></div>}>
            <FAQ />
          </Suspense>
        </main>

        <Suspense fallback={<div className="footer-skeleton" style={{ height: '200px', background: '#f0f0f2' }}></div>}>
          <Footer />
        </Suspense>

        {/* Модальное окно квиза */}
        <QuizModal
          isOpen={isQuizModalOpen}
          onClose={closeQuizModal}
          showFullQuiz={showFullQuiz}
        />

        {/* Фиксированная кнопка "Получить подарок" - ВРЕМЕННО ОТКЛЮЧЕНА */}
        {/* eslint-disable-next-line no-constant-binary-expression */}
        {false && <GiftButton onClick={() => openQuizModal(true)} />}

        {/* Мобильное меню с гамбургером */}
        <MobileMenu />
      </div>
    </>
  )
}

// Основная функция App с провайдером
function App() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  )
}

export default App
