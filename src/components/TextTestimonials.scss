@use '../styles/variables' as *;

.text-testimonials {
  background-color: $background-light;
  position: relative;
  overflow: hidden;
  padding-top: 3rem;
  margin-top: 0;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba($blue, 0.1) 0%, transparent 70%);
    z-index: 0;
  }
    text-align: center;
    @media (max-width: $breakpoint-lg ) {
  padding: 2rem 0 1rem 0;
  background-color: #f1f5f9;
  }
}

.text-testimonials-container {

  @media (max-width: $breakpoint-sm - 1) {
    padding-right: 0;
  }
}

.text-testimonials-header {
  margin-bottom: $spacing-lg;
}

.text-testimonials-title {
  font-size: $font-size-2xl;
  font-weight: 600;
  color: $dark-text;
  margin-bottom: $spacing-sm;
  text-align: center;
    @media (max-width: $breakpoint-lg ) {
   font-size: 1.75rem;
  }
}

.text-testimonials-subtitle {
  font-size: $font-size-md;
  color: $text-color;
  text-align: center;
  max-width: 800px;
  margin: 0 auto $spacing-lg;
  line-height: 1.6;

  @media (max-width: $breakpoint-lg) {
    font-size:1.2rem;
  }
}

.text-testimonials-container {
  position: relative;
  margin-bottom: $spacing-xl;

  @media (max-width: $breakpoint-lg) {
   
    width: 100vw;
    overflow: visible;
      padding: 0 1rem;
    position: relative;
  }
}

.text-testimonials-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $spacing-lg;
  margin-bottom: $spacing-xl;

  @media (max-width: $breakpoint-lg - 1) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: $breakpoint-sm - 1) {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    scroll-padding: $spacing-md;
    gap: $spacing-md;
    padding-bottom: $spacing-md;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.text-testimonial-card {
  background-color: $white;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-md;
  transition: all $transition-normal;
  height: 100%;

  @media (max-width: $breakpoint-lg) {
    flex-shrink: 0;
    width: 70vw;
    max-width: 300px;
    scroll-snap-align: start;

    &:first-child {
   
    }

    &:last-child {
      margin-right: $spacing-md;
    }
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: $shadow-lg;
  }
}

.text-testimonial-content {
  padding: $spacing-lg;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.text-testimonial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
}

.text-testimonial-source {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  &-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  &-name {
    font-size: $font-size-sm;
    color: $text-color;
    font-weight: 500;
  }
}

.text-testimonial-rating {
  display: flex;
  gap: 2px;

  .star-filled {
    color: #FFD700;
    stroke-width: 2px;
  }

  .star-empty {
    color: $border-color;
    stroke-width: 2px;
  }
}

.text-testimonial-quote {
  position: relative;
  margin-bottom: $spacing-md;
  flex-grow: 1;

  .quote-icon {
    color: rgba($primary-color, 0.1);
    position: absolute;
    top: -5px;
    left: -5px;
    stroke-width: 1.5px;
  }
}

.text-testimonial-text {
  font-size: $font-size-sm;
  color: $text-color;
  position: relative;
  z-index: 1;
  padding-left: $spacing-md;
  line-height: 1.5;
   text-align: left;
  margin-bottom: $spacing-md;
 
}

.text-testimonial-author {
  border-top: 1px solid $border-color;
  padding-top: $spacing-md;
  margin-top: auto;
     text-align: left;
  margin-bottom: $spacing-md;
}

.author-name {
  font-size: $font-size-base;
  font-weight: 600;
  margin-bottom: $spacing-xs;
  color: $dark-text;
}

.author-role {
  font-size: $font-size-xs;
  color: $text-color;
  margin-bottom: 0;
}


