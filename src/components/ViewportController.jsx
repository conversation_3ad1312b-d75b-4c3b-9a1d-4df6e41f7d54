import { useEffect, useLayoutEffect } from 'react';

/**
 * Компонент для управления viewport на маленьких экранах
 * Если ширина экрана меньше минимальной (360px), устанавливает фиксированную ширину viewport
 * Если ширина экрана больше или равна минимальной, использует стандартный адаптивный viewport
 */
const ViewportController = ({ minWidth = 360 }) => {
  // Используем useLayoutEffect вместо useEffect, чтобы изменения применялись до отрисовки
  useLayoutEffect(() => {
    // Функция для обновления viewport
    const updateViewport = () => {
      const viewportMeta = document.querySelector('meta[name="viewport"]');

      if (!viewportMeta) {
        console.warn('Viewport meta tag not found');
        return;
      }

      // Получаем текущую ширину экрана
      const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;

      // Если ширина экрана меньше минимальной, устанавливаем фиксированную ширину viewport
      if (screenWidth < minWidth) {
        // Устанавливаем фиксированную ширину viewport и запрещаем масштабирование
        viewportMeta.setAttribute('content', `width=${minWidth}, initial-scale=${screenWidth/minWidth}, maximum-scale=${screenWidth/minWidth}, user-scalable=no`);
        console.log(`ViewportController: Fixed width ${minWidth}px, scale ${screenWidth/minWidth}`);
      } else {
        // Иначе используем стандартный адаптивный viewport
        viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=5.0');
        console.log(`ViewportController: Responsive viewport`);
      }
    };

    // Обновляем viewport при загрузке страницы
    updateViewport();

    // Обновляем viewport при изменении размера окна
    window.addEventListener('resize', updateViewport);

    // Очищаем слушатель при размонтировании компонента
    return () => {
      window.removeEventListener('resize', updateViewport);
    };
  }, [minWidth]);

  // Компонент не рендерит никакой UI
  return null;
};

export default ViewportController;
