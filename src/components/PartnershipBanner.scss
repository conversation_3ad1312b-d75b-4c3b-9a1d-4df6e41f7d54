@use '../styles/variables' as *;

.partnership-banner-section {
  background-color: $slate-lighter;
  padding: $spacing-2xl 0;
  color:$slate-dark ;
    @media (max-width: $breakpoint-lg) {
   padding: 3rem 0;
  }
}

.partnership-banner {
  display: flex;
  flex-direction: column;

  @media (min-width: $breakpoint-lg) {
    flex-direction: row;
    align-items: center;
  }
}

.partnership-banner-image-container {
  width: 100%;
  margin-bottom: $spacing-xl;

  @media (min-width: $breakpoint-lg) {
    width: 40%;
    margin-bottom: 0;
  }
}

.partnership-banner-image-wrapper {
  position: relative;
  border-radius: $border-radius-lg;
  overflow: hidden;
  height: 0;
  padding-bottom: 75%; // 4:3 aspect ratio

  @media (min-width: $breakpoint-lg) {
    height: 100%;
  }
}

.partnership-banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  picture {
    width: 100%;
    height: 100%;
    display: block;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.partnership-banner-image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 30%;
  background: linear-gradient(to right, rgba($blue-dark, 0) 0%, $blue-dark 100%);

  @media (max-width: $breakpoint-lg) {
    width: 100%;
    height: 30%;
    top: auto;
    background: linear-gradient(to bottom, rgba($blue-dark, 0) 0%, $blue-dark 100%);
  }
}

.partnership-banner-content {
  padding: 0 $spacing-md;

  @media (min-width: $breakpoint-lg) {
    width: 60%;
    padding: 0 0 0 $spacing-2xl;
  }
}

.partnership-banner-title {
  font-size: $font-size-2xl;
  font-weight: 700;
  margin-bottom: $spacing-xl;
   color:$slate-dark ;


  @media (max-width: $breakpoint-lg) {
   font-size: 2rem;
           text-align: center;
  }
}

.partnership-banner-benefits {
  list-style: none;
  padding: 0;
  margin: 0 0 $spacing-xl 0;
}

.partnership-banner-benefit-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: $spacing-md;

   @media (max-width: $breakpoint-lg) {
   align-items: center;
    text-align: center;
    margin-bottom: 1rem;
    flex-direction: column;
    font-size: 1.2rem;
  }

  .benefit-check-icon {
    color: $green;
    margin-right: $spacing-sm;
    flex-shrink: 0;
  }

  span {
    font-size: $font-size-md;
    line-height: 1.5;
  }
}

.partnership-banner-cta {
  margin-top: $spacing-xl;
}
