@use '../styles/variables' as *;

.quiz-container-wrapper {
  background-color: $background-light;
  position: relative;
  overflow: hidden;
  padding: $spacing-2xl 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba($primary-color, 0.05) 0%, transparent 70%);
    z-index: 0;
  }
}

.quiz-header {
  position: relative;
  z-index: 1;
  margin-bottom: $spacing-2xl;
}

.quiz-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: $background-light;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  text-align: center;
  position: relative;
  z-index: 1;
}

.quiz-cta {
  display: flex;
  justify-content: center;
  margin: $spacing-lg 0;

  &-button {
    min-width: 300px;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: $shadow-md;
  }
  50% {
    transform: scale(1.05);
    box-shadow: $shadow-lg;
  }
  100% {
    transform: scale(1);
    box-shadow: $shadow-md;
  }
}

.quiz-progress {
  padding: $spacing-md;
  background-color: rgba($primary-color, 0.05);

  &-bar {
    height: 8px;
    background-color: rgba($primary-color, 0.1);
    border-radius: $border-radius-full;
    overflow: hidden;
    margin-bottom: $spacing-sm;
  }

  &-fill {
    height: 100%;
    background-color: $primary-color;
    border-radius: $border-radius-full;
    transition: width 0.5s ease;
  }

  &-text {
    font-size: $font-size-sm;
    color: $text-color;
    text-align: right;
  }
}

.quiz-content {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.quiz-question {
  padding: $spacing-xl;

  &-title {
    font-size: $font-size-xl;
    font-weight: 700;
    margin-bottom: $spacing-lg;
    color: $dark-text;
  }
}

.quiz-options {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;
}

.quiz-option {
  position: relative;

  &-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    &:checked + .quiz-option-label {
      background-color: rgba($primary-color, 0.1);
      border-color: $primary-color;

      &::before {
        opacity: 1;
      }
    }
  }

  &-label {
    display: block;
    padding: $spacing-md;
    border: 2px solid $border-color;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all $transition-normal;
    padding-left: 50px;
    position: relative;

    &:hover {
      border-color: rgba($primary-color, 0.5);
    }

    &::before {
      content: '';
      position: absolute;
      left: $spacing-md;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      border: 2px solid $primary-color;
      border-radius: 50%;
      background-color: $background-light;
      opacity: 0.5;
      transition: all $transition-normal;
    }

    &::after {
      content: '';
      position: absolute;
      left: calc(#{$spacing-md} + 5px);
      top: 50%;
      transform: translateY(-50%);
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: $primary-color;
      opacity: 0;
      transition: all $transition-normal;
    }
  }

  input[type="radio"]:checked + .quiz-option-label::after {
    opacity: 1;
  }

  input[type="checkbox"] + .quiz-option-label::before {
    border-radius: $border-radius-sm;
  }

  input[type="checkbox"]:checked + .quiz-option-label::after {
    opacity: 1;
    border-radius: 0;
    width: 6px;
    height: 12px;
    border: solid $primary-color;
    border-width: 0 2px 2px 0;
    transform: translateY(-65%) rotate(45deg);
    left: calc(#{$spacing-md} + 7px);
  }
}

.quiz-actions {
  display: flex;
  justify-content: space-between;
  gap: $spacing-md;
}

.quiz-contact-form {
  padding: $spacing-xl;
}

.quiz-form {
  &-title {
    font-size: $font-size-xl;
    font-weight: 700;
    margin-bottom: $spacing-md;
    color: $dark-text;
  }

  &-subtitle {
    font-size: $font-size-md;
    color: $text-color;
    margin-bottom: $spacing-md;
  }
}

.quiz-benefits-list {
  list-style: none;
  padding: 0;
  margin: 0 0 $spacing-lg 0;
}

.quiz-benefit-item {
  position: relative;
  padding-left: 30px;
  margin-bottom: $spacing-sm;
  font-size: $font-size-base;
  color: $text-color;

  &::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: $accent-color;
    font-weight: 700;
  }
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  &.form-checkbox {
    flex-direction: row;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.form-label {
  font-size: $font-size-base;
  font-weight: 500;
  color: $dark-text;
}

.form-input {
  padding: $spacing-sm $spacing-md;
  border: 2px solid $border-color;
  border-radius: $border-radius-md;
  font-size: $font-size-base;
  transition: all $transition-normal;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }
}

.form-checkbox {
  &-input {
    margin-top: 3px;
  }

  &-label {
    font-size: $font-size-sm;
    color: $text-color;
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: $spacing-md;
  gap: $spacing-md;
}

.quiz-success {
  padding: $spacing-2xl;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: $accent-color;
  color: $light-text;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-2xl;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-md;
}

.success-title {
  font-size: $font-size-2xl;
  font-weight: 700;
  margin-bottom: $spacing-md;
  color: $dark-text;
}

.success-message {
  font-size: $font-size-md;
  color: $text-color;
  max-width: 500px;
  margin: 0 auto;
}

.quiz-modal-form-actions .button-container{
  width: 100%;
}