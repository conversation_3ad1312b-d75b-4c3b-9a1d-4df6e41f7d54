@use '../../styles/variables' as *;

.circular-progress-loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;

  .circular-progress-svg {
    transform: rotate(-90deg);
    overflow: visible;
  }

  .circular-progress-background {
    fill: none;
    stroke: rgba($slate-light, 0.15);
    stroke-width: 4;
  }

  .circular-progress-indicator {
    fill: none;
    stroke: $primary-color;
    stroke-linecap: round;
    stroke-width: 4;
    transition: stroke-dashoffset 0.3s ease;
  }

  .circular-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 14px;
    font-weight: 500;
    color: $dark-text;
    font-family: $font-family-main;
  }
}
