@use '../../styles/variables' as *;

.quiz-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba($dark-text, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: $spacing-md;
  overflow-y: auto;
  backdrop-filter: blur(5px);

  @media (max-width: $breakpoint-sm) {
    padding: 0;
  }
}

.quiz-modal {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-xl;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;

  @media (max-width: $breakpoint-sm) {
    max-width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }
}

.quiz-modal-close {
  position: absolute;
  top: $spacing-md;
  right: $spacing-md;
  background: none;
  border: none;
  color: $text-color;
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all $transition-fast;

  &:hover {
    background-color: rgba($dark-text, 0.05);
    color: $dark-text;
  }
}

.quiz-modal-header {
  padding: 2rem 5rem 1rem 2rem ;
       @media (max-width: $breakpoint-lg ) {
   padding: 2rem 4rem 1rem 1rem;

  }
}

.quiz-modal-progress {
  margin-top: $spacing-md;

  &-bar {
    height: 6px;
    background-color: rgba($primary-color, 0.1);
    border-radius: $border-radius-full;
    overflow: hidden;
    margin-bottom: $spacing-xs;
  }

  &-fill {
    height: 100%;
    background-color: $primary-color;
    border-radius: $border-radius-full;
    transition: width 0.5s ease;
  }

  &-text {
    font-size: $font-size-xs;
    color: $text-color;
    text-align: right;
  }
}

.quiz-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: $spacing-md $spacing-xl $spacing-xl;
  @media (max-width: $breakpoint-lg ) {
   padding: 1rem;
  }
}

.quiz-modal-intro {
  text-align: center;
  padding: $spacing-xl 0;

  &-title {
    font-size: $font-size-2xl;
    font-weight: 700;
    color: $dark-text;
    margin-bottom: $spacing-md;

  }

  &-subtitle {
    font-size: $font-size-md;
    color: $text-color;
    margin-bottom: $spacing-xl;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }

  &-benefits {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    margin-bottom: $spacing-xl;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  &-benefit {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    text-align: left;
    font-size: $font-size-base;

    .benefit-icon-wrapper {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: rgba($accent-color, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .benefit-icon {
        color: $accent-color;
      }
    }
  }

  &-button {
    min-width: 200px;
    margin-top: $spacing-md;
  }
}

.quiz-modal-question {
  &-title {
    font-size: $font-size-lg;
    font-weight: 600;
    margin-bottom: $spacing-lg;
    color: $dark-text;
  }
}

.quiz-modal-options {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;
}

.quiz-modal-option {
  position: relative;

  &-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    &:checked + .quiz-modal-option-label {
      background-color: rgba($primary-color, 0.1);
      border-color: $primary-color;

      &::before {
        opacity: 1;
      }
    }
  }

  &-label {
    display: block;
    padding: $spacing-md;
    border: 2px solid $border-color;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all $transition-normal;
    padding-left: 50px;
    position: relative;

    &:hover {
      border-color: rgba($primary-color, 0.5);
    }

    &::before {
      content: '';
      position: absolute;
      left: $spacing-md;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      border: 2px solid $primary-color;
      border-radius: 50%;
      background-color: $white;
      opacity: 0.5;
      transition: all $transition-normal;
    }

    &::after {
      content: '';
      position: absolute;
      left: calc(#{$spacing-md} + 5px);
      top: 50%;
      transform: translateY(-50%);
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: $primary-color;
      opacity: 0;
      transition: all $transition-normal;
    }
  }

  input[type="radio"]:checked + .quiz-modal-option-label::after {
    opacity: 1;
  }

  input[type="checkbox"] + .quiz-modal-option-label::before {
    border-radius: $border-radius-sm;
  }

  input[type="checkbox"]:checked + .quiz-modal-option-label::after {
    opacity: 1;
    border-radius: 0;
    width: 6px;
    height: 12px;
    border: solid $primary-color;
    border-width: 0 2px 2px 0;
    transform: translateY(-65%) rotate(45deg);
    left: calc(#{$spacing-md} + 7px);
  }
}

.quiz-modal-actions {
  display: flex;
  justify-content: space-between;
  gap: $spacing-md;
}

.quiz-modal-contact-form {
  padding: $spacing-md 0;
}

.quiz-modal-form {
  &-title {
    font-size: $font-size-lg;
    font-weight: 600;
    margin-bottom:0.75rem;
    color: $dark-text;
    position: relative;
     @media (max-width: $breakpoint-lg ) {
   padding: 0 3rem 0.5rem 0;
  }
  }

  &-subtitle {
    font-size: $font-size-base;
    color: $text-color;
    margin-bottom: $spacing-md;
    text-align: center;
  }

  &-actions {
    display: flex;
    flex-direction: column;
    margin-top: 1rem;
    gap: $spacing-md;

    .submit-button {
      flex: 1;
      padding: 1.25rem 2rem;
          width: 100%;
    }


  }

  .form-error {
    color: $error-color;
    background-color: rgba($error-color, 0.1);
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-md;
    font-size: $font-size-sm;
    margin-bottom: $spacing-sm;
    width: 100%;
  }
}

.quiz-modal-benefits-list {
  list-style: none;
  padding: 0 0 0.75rem 0;
  margin: 0 0 1rem 0;
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid rgba($text-color, 0.1);
  gap: 2rem;
       @media (max-width: $breakpoint-lg ) {
   flex-direction: column;
    display: block;
  }
}

.quiz-modal-benefit-item {
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;
  font-size: 0.83rem;

  color: $text-color;
  margin-bottom: 0;

         @media (max-width: $breakpoint-lg ) {
      margin-bottom:0.5rem;
  }

  .benefit-icon-wrapper {
    width: 1rem;
    height: 1rem; // Изменено на 24px;
    border-radius: 50%;
    background-color: rgba($accent-color, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .benefit-icon {
    color: $accent-color;
    flex-shrink: 0;
    width: 12px;
    height: 12px;
  }

  .benefit-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
     @media (max-width: $breakpoint-lg) {
      font-size: 1rem;
     }
  }

  .benefit-highlight {
    font-weight: 600;
    color: $accent-color;
  }
}

.quiz-modal-form {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  width: 100%;

  &.form-checkbox {
    flex-direction: row;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.input-wrapper {
  width: 100%;
  position: relative;

  .error-message {
    color: $error-color;
    font-size: $font-size-xs;
    margin-top: 0.25rem;
    display: block;
    position: relative;
    width: 100%;
    text-align: left;
  }
}

.form-error-message {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  color: $error-color;
  background-color: rgba($error-color, 0.1);
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  margin-bottom: $spacing-md;
  width: 100%;
}

.form-label {
  font-size: $font-size-base;
  font-weight: 500;
  color: $dark-text;
  .required-field{
    color:$red;
    font-size: 0.83rem;
  }
}

.form-input {
  padding: 0.6rem 1rem;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  font-size: $font-size-base;
  transition: all $transition-normal;
  width: 100%;

  &:focus {
    outline: none;
    border-color: $slate;
    box-shadow: 0 0 0 3px rgba($slate, 0.1);
  }

  &.input-error {
    border-color: $error-color;

    &:focus {
      box-shadow: 0 0 0 3px rgba($error-color, 0.1);
    }
  }
}

.phone-input-wrapper {
  display: flex;
  align-items: stretch; /* Изменено с center на stretch */
  width: 100%;

  .phone-prefix {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    padding: 0 0.75rem; /* Убрали вертикальные отступы */
    background-color: $light-bg;
    border: 1px solid $border-color;
    border-right: none;
    border-radius: $border-radius-md 0 0 $border-radius-md;
    min-width: 60px; /* Фиксированная минимальная ширина */
    justify-content: center; /* Центрируем содержимое */

    .country-flag {
      width: 16px;
      height: auto;
    }

    span {
      font-size: $font-size-base;
      font-weight: 500;
    }
  }

  .phone-input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    height: 100%; /* Гарантируем полную высоту */
  }
}

.form-checkbox {
  &-input {
    margin-top: 3px;
  }

  &-label {
    font-size: $font-size-sm;
    color: $text-color;
  }
}

.quiz-modal-success {
  padding: 2rem 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: $accent-color;
  color: $white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-md;
}

.success-title {
  font-size: $font-size-xl;
  font-weight: 700;
  margin-bottom: $spacing-md;
  color: $dark-text;
}

.success-message {
  font-size: $font-size-md;
  color: $text-color;

  margin: 0 auto;
  margin-bottom: $spacing-md;
}

.check-email-button {
  display: inline-block;
  background-color: $success-color;
  color: white;
  font-weight: 600;
  padding: 1.25rem 3rem;
  border-radius: 0.75rem;
  text-decoration: none;
  transition: background-color 0.2s ease;
  margin-top: $spacing-md;
  box-shadow: $shadow-sm;

  &:hover {
    background-color: darken($success-color, 5%);
      text-decoration: none;
      color: #fff;
  }

  &:active {
    transform: translateY(1px);
  }
}
