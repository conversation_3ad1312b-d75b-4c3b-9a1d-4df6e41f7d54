import React, { useRef, useEffect, useState } from 'react';
import Hls from 'hls.js/dist/hls.min.js';
import SpinnerLoader from './SpinnerLoader';
import './LazyVideo.scss';

// Стандартные настройки HLS.js по рекомендациям
const HLS_CONFIG = {
  // Базовые настройки
  autoStartLoad: true,           // Автоматически начинать загрузку
  startLevel: -1,                // Автоматический выбор начального качества

  // Настройки буфера
  maxBufferLength: 30,           // Стандартный размер буфера (30 секунд)
  maxMaxBufferLength: 60,        // Максимальный размер буфера (60 секунд)

  // Настройки адаптивного битрейта
  capLevelToPlayerSize: true,    // Выбирать качество в зависимости от размера плеера

  // Настройки загрузки
  progressive: true,             // Включаем прогрессивную загрузку

  // Отключаем отладку
  debug: false
};

const TestimonialVideo = ({ src, poster, alt }) => {
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const hlsRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [loadProgress, setLoadProgress] = useState(0);

  // Функция для безопасного обновления прогресса загрузки
  const updateProgress = (value) => {
    if (typeof value === 'number' && !isNaN(value)) {
      setLoadProgress(Math.min(Math.max(value, 0), 100));
    }
  };

  // Эффект для инициализации видео
  useEffect(() => {
    // Извлекаем ID видео из src
    const videoId = src.replace('/images/testimonials/', '').replace('.mp4', '');

    const video = videoRef.current;
    if (!video) return;

    // Базовые настройки видео
    video.muted = false;
    video.controls = true;
    video.playsInline = true;
    video.preload = 'auto';

    // Получаем HLS URL
    const hlsUrl = `/images/testimonials/${videoId}/playlist.m3u8`;

    // Обработчики событий
    const handleCanPlay = () => {
      video.play().catch(error => {
        console.error('Autoplay failed:', error);
      });
    };

    const handlePlaying = () => {
      setIsPlaying(true);
      setIsLoading(false);
    };

    const handleError = (e) => {
      console.error('Video error:', e);
      setIsLoading(false);
      setHasError(true);
    };

    const handleProgress = () => {
      if (video.buffered.length > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        if (duration > 0) {
          updateProgress((bufferedEnd / duration) * 100);
        }
      }
    };

    // Добавляем обработчики событий
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('playing', handlePlaying);
    video.addEventListener('error', handleError);
    video.addEventListener('progress', handleProgress);
    video.addEventListener('loadedmetadata', handleProgress);

    // Инициализация HLS
    if (Hls.isSupported()) {
      // Создаем новый экземпляр HLS
      const hls = new Hls(HLS_CONFIG);
      hlsRef.current = hls;

      // Загружаем HLS
      hls.loadSource(hlsUrl);
      hls.attachMedia(video);

      // Обработка событий HLS
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        // Если видео уже загружено, начинаем воспроизведение
        if (video.readyState >= 3) {
          handleCanPlay();
        }
      });

      // Обработка ошибок
      hls.on(Hls.Events.ERROR, (_, data) => {
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              hls.startLoad(); // Пробуем перезагрузить
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              hls.recoverMediaError(); // Восстанавливаемся после ошибки медиа
              break;
            default:
              handleError(data);
              break;
          }
        }
      });

      // Отслеживание прогресса загрузки
      hls.on(Hls.Events.FRAG_BUFFERED, (_, data) => {
        if (data.stats.total > 0) {
          updateProgress((data.stats.loaded / data.stats.total) * 100);
        }
      });
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Нативная поддержка HLS (Safari)
      video.src = hlsUrl;
    } else {
      // Фоллбэк на обычное MP4 видео
      video.src = src;
    }

    // Очистка при размонтировании
    return () => {
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('playing', handlePlaying);
      video.removeEventListener('error', handleError);
      video.removeEventListener('progress', handleProgress);
      video.removeEventListener('loadedmetadata', handleProgress);

      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src]);

  return (
    <div className="lazy-video-container" ref={containerRef}>
      {isLoading && (
        <div className="video-loading">
          <SpinnerLoader size={48} />
        </div>
      )}
      {hasError && (
        <div className="video-error">
          <span className="video-error-icon">!</span>
          <span className="video-error-text">Ошибка загрузки видео</span>
        </div>
      )}
      <video
        ref={videoRef}
        className={`lazy-video ${isPlaying ? 'playing' : ''}`}
        playsInline
        controls
        poster={poster ? poster.replace(/\.(jpg|jpeg|png)$/i, '.webp') : undefined}
        preload="auto"
      >
        {alt}
      </video>
    </div>
  );
};

export default TestimonialVideo;
