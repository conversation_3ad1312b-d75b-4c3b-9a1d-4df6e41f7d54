@use '../../styles/variables' as *;

.spinner-loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .spinner-svg {
    animation: rotate 1.5s linear infinite;
  }
  
  .spinner-circle {
    stroke: $primary-color;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: 280;
    animation: dash 1.5s ease-in-out infinite;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 280;
  }
  50% {
    stroke-dashoffset: 75;
  }
  100% {
    stroke-dashoffset: 280;
  }
}
