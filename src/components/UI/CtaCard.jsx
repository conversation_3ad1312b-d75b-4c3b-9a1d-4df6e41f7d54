import React from 'react';
import { CloudDownload, FileCode, Sheet } from 'lucide-react'; // Удалили DatabaseZap
import qParserIcon from '../../assets/Q-parser.svg'; // Импортируем иконку Q-parser
import './CtaCard.scss';

const CtaCard = ({ title, description, subtitle, onClick, className }) => {
  const mainTitle = title || "Каталог доступен";
  const mainDescription = description || "";
  const mainSubtitle = subtitle || "";

  const exportTypes = [
    { iconType: 'custom', iconSrc: qParserIcon, name: 'Q-parser', colorClass: 'icon-color-api' }, // Используем кастомную иконку
    { iconType: 'lucide', Icon: CloudDownload, name: 'CloudParser', colorClass: 'icon-color-cloud' },
    { iconType: 'lucide', Icon: FileCode, name: 'Y<PERSON>', colorClass: 'icon-color-yml' },
    { iconType: 'lucide', Icon: Sheet, name: 'CS<PERSON>', colorClass: 'icon-color-csv' },
  ];

  // Добавляем класс product-card-cta для согласованности стилей с ProductCard
  const cardClassName = `cta-card product-card-cta ${className || ''}`;

  const handleKeyDown = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      onClick();
      event.preventDefault();
    }
  };

  return (
    <div
      className={cardClassName}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex="0"
      aria-label={`${mainTitle}. Нажмите чтобы выгрузить каталог`}
    >
      <div className="cta-top-content">
        <h3 className="cta-main-title">
          {mainTitle}
        </h3>
        {mainDescription && <div className="cta-description">{mainDescription}</div>}
        {mainSubtitle && <div className="cta-subtitle">{mainSubtitle}</div>}
      </div>

      <div className="cta-bottom-content">
        {/* Используем тот же map, но добавляем colorClass */}
        <div className="cta-export-types">
          {exportTypes.map((item) => (
            <div key={item.name} className="cta-export-type">
              {item.iconType === 'custom' ? (
                <img
                  src={item.iconSrc}
                  alt={item.name}
                  className={`cta-export-icon ${item.colorClass}`}
                  width="28"
                  height="28"
                />
              ) : (
                <item.Icon
                  className={`cta-export-icon ${item.colorClass}`}
                  size={28}
                  strokeWidth={1.5}
                />
              )}
              <span className="cta-export-name">{item.name}</span>
            </div>
          ))}
        </div>
        <p className="cta-hint">Нажмите, чтобы выгрузить каталог</p>
      </div>
    </div>
  );
};

export default CtaCard;