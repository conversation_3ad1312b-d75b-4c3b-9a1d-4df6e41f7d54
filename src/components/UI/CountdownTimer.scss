@use '../../styles/variables' as *;

// Новый инлайн таймер
.countdown-inline {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  gap: $spacing-xs;
  font-weight: normal;
  
  margin: 0;

  @media (max-width: $breakpoint-md) {
    justify-content: center;
  }
}

.countdown-text {
  font-weight: 700;
}

.countdown-days {
  font-weight: 400;
}

.countdown-time {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: $primary-color;
  color: $white;
  padding: 0.2em 0.5em;
  border-radius: 4px;
  font-weight: 700;
  min-width: 110px; // Фиксированная минимальная ширина
  text-align: center; // Центрирование текста
  font-family: monospace; // Моноширинный шрифт для равной ширины цифр

  .countdown-flame-icon {
    margin-right: 0.3em;
    color: $white;
    flex-shrink: 0; // Иконка не должна сжиматься
  }
}

// Стили для прилипающего таймера (десктоп)
.sticky-countdown-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: $z-index-sticky;
  background: #FCEDA2; // Желтый фон
  color: $black; // Черный текст
min-height: 4rem;
  display: flex;
  align-items: center; // вертикальное выравнивание

  // Показываем только на десктопе
  @media (max-width: $breakpoint-lg) {
    display: none;
  }

  .sticky-countdown {
    display: flex;
    justify-content: center;
    align-items: center;
    color: $black; // Черный текст

    .countdown-text {
      color: $black; // Черный текст
    }

    .countdown-days {
      color: $black; // Черный текст
    }

    .countdown-time {
      background-color: $primary-color; // Красный фон
      color: $white; // Белый текст
      font-size: 0.85rem;
      padding: 0.15em 0.4em;
    }
  }
}

// Стили для мобильного таймера
.mobile-countdown-container {
  margin: $spacing-lg 0;

  // Показываем только на мобильных
  @media (min-width: $breakpoint-lg) {
    display: none;
  }

  .mobile-countdown {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    text-align: center;
    background: #FCEDA2; // Желтый фон как на десктопе
    color: $black; // Черный текст как на десктопе
    padding: 1rem;
    border-radius: $border-radius-md;
  

    .countdown-text {
      font-size: 1.2rem;
      color: $black; // Черный текст
      margin-right: $spacing-xs;
    }

    .countdown-days {
      font-size: 1.1rem;
      color: $black; // Черный текст
      margin-right: $spacing-xs;
    }

    .countdown-time {
      background-color: $primary-color; // Красный фон как на десктопе
      color: $white; // Белый текст как на десктопе
      font-size: 0.9rem;
      padding: 0.2em 0.5em;
      border-radius: 4px;
    }
  }
}
