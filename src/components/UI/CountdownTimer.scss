@use '../../styles/variables' as *;

// Новый инлайн таймер
.countdown-inline {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  gap: $spacing-xs;
  font-weight: normal;
   background-color: rgba($yellow, 0.1);
   padding:1rem;
   border-radius: 1rem;
  margin: 0;

  @media (max-width: $breakpoint-md) {
    justify-content: center;
  }
}

.countdown-text {
  font-weight: 700;
}

.countdown-days {
  font-weight: 700;
}

.countdown-time {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: $primary-color;
  color: $white;
  padding: 0.2em 0.5em;
  border-radius: 4px;
  font-weight: 700;
  min-width: 110px; // Фиксированная минимальная ширина
  text-align: center; // Центрирование текста
  font-family: monospace; // Моноширинный шрифт для равной ширины цифр

  .countdown-flame-icon {
    margin-right: 0.3em;
    color: $white;
    flex-shrink: 0; // Иконка не должна сжиматься
  }
}
