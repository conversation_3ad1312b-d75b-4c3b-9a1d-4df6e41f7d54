import React from 'react';
import './SpinnerLoader.scss';

/**
 * Компонент SpinnerLoader - минималистичный циклический лоадер без процентов
 * для использования в видео отзывах
 */
const SpinnerLoader = ({ size = 64 }) => {
  return (
    <div className="spinner-loader" style={{ width: size, height: size }}>
      <svg
        className="spinner-svg"
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
      >
        <circle
          className="spinner-circle"
          cx={size / 2}
          cy={size / 2}
          r={(size - 8) / 2}
          fill="none"
          strokeWidth="4"
        />
      </svg>
    </div>
  );
};

export default SpinnerLoader;
