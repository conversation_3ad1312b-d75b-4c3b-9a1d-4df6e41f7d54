import React from 'react';
import './Button.scss';

const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  tooltip = '',
  ...props
}) => {
  const buttonClasses = `
    button
    button-${variant}
    button-${size}
    ${fullWidth ? 'button-full-width' : ''}
    ${tooltip ? 'button-with-tooltip' : ''}
    ${className}
  `.trim();

  return (
    <div className="button-container">
      {tooltip && <div className="button-tooltip">{tooltip}</div>}
      <button
        type={type}
        className={buttonClasses}
        onClick={onClick}
        disabled={disabled}
        {...props}
      >
        {children}
      </button>
    </div>
  );
};

export default Button;
