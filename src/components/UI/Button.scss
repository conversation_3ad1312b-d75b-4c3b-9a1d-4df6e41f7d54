@use '../../styles/variables' as *;
@use 'sass:color';

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border: none;
  border-radius: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  font-family: $font-family-main;
  letter-spacing: 0.01em;


  // Content inside the button should not be skewed
  & > * {
    transform: none;
    display: inline-block;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
  }

  &:focus {
    outline: none;
  }

  &:active::after {
    animation: ripple 0.6s ease-out;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Button sizes
.button-small {
  padding: $spacing-xs $spacing-lg;
  font-size: $font-size-sm;
  height: 48px;
}

.button-medium {
  padding: $spacing-sm $spacing-xl;
  font-size: $font-size-base;
  height: 52px;
}

.button-large {
  padding: $spacing-md $spacing-2xl;
  font-size: $font-size-md;
  height: 64px;
}

// Button variants
.button-primary {
  background-color: $red;
  color: $light-text;
  box-shadow: 0 4px 12px rgba($red, 0.25);
  position: relative;
  overflow: hidden;
  z-index: 1;
  font-weight: 700;
  letter-spacing: 0;
  border-radius: 0.75rem;
  animation: subtle-pulse 2s infinite;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
    z-index: -1;
  }

  &:hover:not(:disabled) {
    box-shadow: 0 8px 20px rgba($red, 0.4);
    background-color: color.adjust($red, $lightness: 5%);
    animation: none;

    &::before {
      transform: translateX(100%);
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 4px 8px $button-shadow;
  }
}

@keyframes subtle-pulse {
  0% {
    box-shadow: 0 4px 12px rgba($red, 0.25);
  }
  50% {
    box-shadow: 0 4px 16px rgba($red, 0.4);
  }
  100% {
    box-shadow: 0 4px 12px rgba($red, 0.25);
  }
}

.button-secondary {
  background-color: transparent;
  border-radius: 0.75rem;
  height: 60px;
  border: 2px solid $slate;
  color:$slate;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  font-family: $font-family-main;
  letter-spacing: 0.01em;
    @media (max-width: $breakpoint-lg) {
          width: 100%;
      }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
    z-index: -1;
  }

  &:hover:not(:disabled) {
  color:$white;
    box-shadow: 0 8px 20px rgba($slate, 0.3);
    background-color: color.adjust($slate, $lightness: 3%);

    &::before {
      transform: translateX(100%);
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba($slate, 0.15);
  }
}

.button-accent {
  background-color: $blue;
  color: $light-text;
  box-shadow: 0 4px 12px rgba($blue, 0.15);
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  z-index: 1;
  font-weight: 700;
  letter-spacing: 0;
  gap: 0.5rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
    z-index: -1;
  }

  &:hover:not(:disabled) {

    box-shadow: 0 8px 20px rgba($blue, 0.3);
    background-color: #1a5cff; // Более светлый оттенок синего

    &::before {
      transform: translateX(100%);
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba($blue, 0.15);
  }
}

.button-outline {
  background-color: transparent;
  color: $blue;
  border: 1px solid $blue;
  position: relative;
  z-index: 1;
  font-weight: 600;
  border-radius: 0.75rem;

  &:hover:not(:disabled) {

    background-color: $blue-light;
    color: $blue-dark;
    box-shadow: 0 4px 12px rgba($blue, 0.1);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: none;
  }
}

.button-outline-secondary {
  background-color: transparent;
  color: $secondary-color;
  border: 2px solid $secondary-color;

  &:hover:not(:disabled) {
    background-color: rgba($secondary-color, 0.1);
    transform: translateY(-2px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

.button-text {
  background-color: transparent;
  color: $primary-color;
  padding: $spacing-xs;

  &:hover:not(:disabled) {
    background-color: rgba($primary-color, 0.1);
  }
}

.button-full-width {
  width: 100%;
}

.slideshow-button {
  transform: skew(-10deg) !important;
}

.button-container {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
    @media (min-width: $breakpoint-lg) {
      width: auto;
    }

}

.button-with-tooltip {
  margin-top: 4px;
}

.button-tooltip {
  font-size: $font-size-sm;
  color: rgba($text-color, 0.9);
  text-align: center;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  font-weight: 400;
  font-size: 1rem;

  
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}
