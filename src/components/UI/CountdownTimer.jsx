import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Flame } from 'lucide-react';
import './CountdownTimer.scss';

// Оптимизированный компонент таймера с DOM-манипуляциями для обновления секунд
const CountdownTimer = memo(({ targetDate, className = '' }) => {
  // Рефы для прямого обновления DOM без перерисовки React
  const secondsRef = useRef(null);
  const minutesRef = useRef(null);
  const hoursRef = useRef(null);
  const daysRef = useRef(null);

  // Рассчитываем начальное время один раз при монтировании
  const initialTimeLeft = useCallback(() => {
    const difference = +new Date(targetDate) - +new Date();

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
    };
  }, [targetDate]);

  // Храним время в состоянии только для начального рендера
  const [timeLeft] = useState(initialTimeLeft);

  // Функция форматирования времени
  const formatTime = useCallback((value) => {
    return value < 10 ? `0${value}` : value;
  }, []);

  // Эффект для обновления секунд через DOM-манипуляции
  useEffect(() => {
    if (!secondsRef.current) return;

    let seconds = timeLeft.seconds;
    let minutes = timeLeft.minutes;
    let hours = timeLeft.hours;
    let days = timeLeft.days;

    // Обновляем секунды каждую секунду через DOM-манипуляции
    const timer = setInterval(() => {
      seconds -= 1;

      if (seconds < 0) {
        seconds = 59;
        minutes -= 1;

        if (minutes < 0) {
          minutes = 59;
          hours -= 1;

          if (hours < 0) {
            hours = 23;
            days -= 1;

            if (daysRef.current) {
              daysRef.current.textContent = `${days} дней`;
            }
          }

          if (hoursRef.current) {
            hoursRef.current.textContent = formatTime(hours);
          }
        }

        if (minutesRef.current) {
          minutesRef.current.textContent = formatTime(minutes);
        }
      }

      if (secondsRef.current) {
        secondsRef.current.textContent = formatTime(seconds);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, formatTime]);

  return (
    <div className={`countdown-inline ${className}`}>
      <strong className="countdown-text">Акция «4+1»: каждый 5-й товар бесплатно! Осталось:</strong>
      <strong className="countdown-days" ref={daysRef}>{timeLeft.days} дня </strong>
      <span className="countdown-time">
        <Flame size={16} className="countdown-flame-icon" />
        <span ref={hoursRef}>{formatTime(timeLeft.hours)}</span>:
        <span ref={minutesRef}>{formatTime(timeLeft.minutes)}</span>:
        <span ref={secondsRef}>{formatTime(timeLeft.seconds)}</span>
      </span>
    </div>
  );
});

export default CountdownTimer;
