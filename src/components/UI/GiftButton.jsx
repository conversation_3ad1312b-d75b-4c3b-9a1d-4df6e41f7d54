import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Gift } from 'lucide-react';
import { throttle } from '../../utils/throttle';
import './GiftButton.scss';

const GiftButton = ({ onClick, scrollTriggerElementId = 'testimonials' }) => {
  const [isVisible, setIsVisible] = useState(false);

  // Создаем мемоизированную функцию обработчика скролла с тротлингом
  const handleScroll = useCallback(
    throttle(() => {
      const triggerElement = document.getElementById(scrollTriggerElementId);
      if (triggerElement) {
        const triggerPosition = triggerElement.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;

        if (triggerPosition <= windowHeight * 0.8) {
          setIsVisible(true);
        } else {
          setIsVisible(false);
        }
      }
    }, 100), // Тротлинг с интервалом 100мс
    [scrollTriggerElementId]
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    // Initial check
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  const buttonVariants = {
    hidden: { opacity: 0, transform: 'translateY(20px)' },
    visible: {
      opacity: 1,
      transform: 'translateY(0)',
      transition: {
        duration: 0.4, // Уменьшенная длительность
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      transform: 'translateY(20px)',
      transition: {
        duration: 0.2, // Уменьшенная длительность
        ease: 'easeIn'
      }
    }
  };

  // Удалена анимация пульсации

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.button
          className="gift-button"
          onClick={onClick}
          variants={buttonVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          style={{ willChange: 'transform, opacity' }}
        >
          <Gift size={20} className="gift-icon" />
          <span className="gift-text">Получить подарок</span>
        </motion.button>
      )}
    </AnimatePresence>
  );
};

export default GiftButton;
