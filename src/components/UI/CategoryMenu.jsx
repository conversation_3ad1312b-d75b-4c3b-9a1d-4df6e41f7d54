import React from 'react';
import './CategoryMenu.scss';
// Импорт sendLeadEvent удален
// Импортируем иконки
import iconKurtki from '../../assets/icons/icon-kurtki.svg';
import iconKostyumyi from '../../assets/icons/icon-kostyumyi.svg';
import iconBryuki from '../../assets/icons/icon-bryuki.svg';
import iconJeans from '../../assets/icons/icon-jeans-optom.svg';
import iconPalto from '../../assets/icons/icon-palto.svg';
import iconFitness from '../../assets/icons/icon-fitness.svg';
import iconUnload from '../../assets/icons/icon- unload.svg';
import iconSales from '../../assets/icons/icon-sales.svg';
import iconMarkdown from '../../assets/icons/icon-markdown.svg';

const CategoryMenu = () => {
  const categories = [
    { id: 'kurtki', name: '<PERSON>у<PERSON><PERSON><PERSON><PERSON>', icon: icon<PERSON><PERSON><PERSON> },
    { id: 'kostyumy<PERSON>', name: 'Костюмы', icon: iconKostyumyi },
    { id: 'bryuki', name: 'Брюки', icon: iconBryuki },
    { id: 'jeans', name: 'Джинсы', icon: iconJeans },
    { id: 'palto', name: 'Пальто', icon: iconPalto },
    { id: 'fitness', name: 'Для фитнеса', icon: iconFitness },
    { id: 'unload', name: 'Выгрузить', icon: iconUnload },
    { id: 'sales', name: 'Распродажи', icon: iconSales },
    { id: 'markdown', name: 'Уценка', icon: iconMarkdown },
  ];

  const handleCategoryClick = (categoryId) => {
    // Вызываем форму с указанием источника
    if (window.openQuizModal) {
      window.openQuizModal(false, 'catalog', categoryId);
    }
  };

  return (
    <div className="category-menu-wrapper">
      <div className="category-menu">
        {categories.map((category) => (
          <button
            key={category.id}
            className="category-item"
            onClick={() => handleCategoryClick(category.id)}
          >
            <div className="category-icon-wrapper">
              <img
                src={category.icon}
                alt={category.name}
                className="category-icon"
                width="20"
                height="20"
              />
            </div>
            <span className="category-name">{category.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryMenu;
