@use '../../styles/variables' as *;

.gift-button {
  position: fixed;
  bottom: $spacing-lg;
  left: $spacing-lg;
  background-color: $primary-color;
  color: $white;
  border: none;
  border-radius: $border-radius-full;
  padding: $spacing-md $spacing-xl;
  font-weight: 700;
  font-size: $font-size-md;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  z-index: 100;
  box-shadow: $shadow-md;
  transition: all $transition-fast;

  &:hover {
    background-color: darken($primary-color, 5%);
  }

  .gift-icon {
    animation: rotate-gift 6s infinite;
    font-size: 1.2em;
  }

  @media (max-width: $breakpoint-sm) {
    bottom: $spacing-md;
    left: $spacing-md;
    right: $spacing-md;
    width: calc(100% - #{$spacing-md * 2});
    border-radius: $border-radius-lg;
    justify-content: center;
    padding: $spacing-md $spacing-lg;
  }
}

@keyframes rotate-gift {
  0% {
    transform: rotate(0deg);
  }
  15% {
    transform: rotate(30deg);
  }
  30% {
    transform: rotate(0deg);
  }
  35% {
    transform: rotate(-30deg);
  }
  40% {
    transform: rotate(0deg);
  }
  /* Пауза 2 секунды */
  70% {
    transform: rotate(0deg);
  }
  80% {
    transform: rotate(-30deg);
  }
  90% {
    transform: rotate(0deg);
  }
  95% {
    transform: rotate(30deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
