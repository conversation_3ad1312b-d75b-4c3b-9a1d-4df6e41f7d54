@use '../../styles/variables' as *;

$icon-container-max-width: 200px;
$icon-gap-horizontal: $spacing-lg;
$icon-gap-vertical: $spacing-md;

.cta-card {
  height: 100%;
  background-color: $white;
  border: 2px dashed $border-color;
  border-radius: $border-radius-lg !important; /* Принудительно устанавливаем border-radius */
  padding: $spacing-lg;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  text-align: center;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  will-change: border-style, background-color, box-shadow; /* Оптимизация для GPU */
  transition: border-style $transition-fast, background-color $transition-normal, box-shadow $transition-normal;

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
  &:focus:not(:focus-visible) {
     outline: none;
  }
  &:focus-visible {
     outline: 2px solid $primary-color;
     outline-offset: 2px;
  }

  .cta-top-content {
    // No specific styles needed
  }

  .cta-main-title {
    font-family: $font-family-main; // Используем основной шрифт вместо шрифта для заголовков
    font-size: $font-size-3xl;
    font-weight: 700;
    line-height: 1.2;
    color: $dark-text;
    margin: 0;
    padding: 0;
    transition: color $transition-normal;
  }

  .cta-description {
    font-family: $font-family-main;
    font-size: $font-size-base;
    color: $text-color;
    margin: $spacing-sm auto;
    padding: 0;
    text-align: center;
    max-width: 90%;
    line-height: 1.5;
    transition: color $transition-normal;
  }

  .cta-subtitle {
    font-family: $font-family-main;
    font-size: $font-size-base;
    color: $text-color;
    margin: 0 auto $spacing-md;
    padding: 0;
    text-align: center;
    max-width: 90%;
    line-height: 1.5;
    transition: color $transition-normal;
  }

  .cta-bottom-content {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .cta-export-types {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    column-gap: $icon-gap-horizontal;
    row-gap: $icon-gap-vertical;
    margin-bottom: $spacing-lg;
    width: 100%;
    max-width: $icon-container-max-width;
    padding: 0; // Убираем внутренние отступы у контейнера флекс элементов
  }

  .cta-export-type {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 0 0 calc(50% - #{$icon-gap-horizontal / 2});
    box-sizing: border-box; // Учитываем padding/border в расчетах ширины
    min-width: 70px;
  }

  .cta-export-icon {
    margin-bottom: $spacing-xs;
    transition: color $transition-fast, transform $transition-fast;

    // Для кастомных SVG иконок
    &[src] {
      width: 28px;
      height: 28px;
      object-fit: contain;
    }

    &.icon-color-api { color: $blue; }
    &.icon-color-cloud { color: $blue; }
    &.icon-color-yml { color: $yellow; }
    &.icon-color-csv { color: $slate-dark; }
  }

  .cta-export-name {
    font-family: $font-family-main;
    font-size: $font-size-base; // Изменено на базовый размер шрифта
    color: $text-color;
    line-height: 1.3;
    transition: color $transition-fast;
  }

  .cta-hint {
    font-family: $font-family-main;
    font-size: $font-size-sm;
    color: $slate;
    margin: 0;
    padding-top: $spacing-xs;
    transition: color $transition-normal; // Убран переход для font-weight
  }

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      border-style: solid;
      border-color: $dark-text; // Черный цвет обводки вместо красного
      background-color: $subtle-bg;
      box-shadow: $shadow-md;

      .cta-main-title {
        color: $dark-text; // Черный цвет текста вместо красного
      }

      .cta-description {
        color: $dark-text; // Черный цвет текста вместо красного
      }

      .cta-subtitle {
        color: $dark-text; // Черный цвет текста вместо красного
      }

      .cta-export-icon {
        transform: scale(1.15);
      }

      .cta-export-name {
        color: $dark-text; // Черный цвет при наведении, а не красный
      }

      .cta-hint {
        color: $dark-text;
        // Убрана жирность при наведении
      }
    }
  }
 @media (max-width: $breakpoint-lg) {
  .cta-bottom-content{
          display: none;
        }

      }
  @media (max-width: $breakpoint-md) {
    padding: $spacing-md;

    .cta-main-title {
      font-size: $font-size-2xl;
      margin-bottom: $spacing-sm;
    }

     .cta-export-types {
        column-gap: $spacing-md;
        row-gap: $spacing-sm;
        margin-bottom: $spacing-sm;
        max-width: 180px;
     }

      .cta-export-type {
         flex-basis: calc(50% - #{$spacing-md / 2});
         min-width: 60px;
      }

     .cta-export-icon {
       width: 24px;
       height: 24px;
       margin-bottom: 2px;

       // Для кастомных SVG иконок на мобильных устройствах
       &[src] {
         width: 24px;
         height: 24px;
       }
     }

     .cta-export-name {
        font-size: $font-size-sm; // Используем переменную вместо фиксированного значения
     }

    .cta-hint {
      font-size: $font-size-xs;
      padding-top: $spacing-xs;
    }
  }

   @media (max-width: $breakpoint-sm) {
       padding: $spacing-lg $spacing-md;
       min-height: 190px;

       .cta-main-title {
         font-size: 2.5rem;
         font-weight: 800;
       }

       .cta-description {
         font-size: 1.2rem;
         margin-bottom: $spacing-md;
       }

       .cta-export-types {
          column-gap: $spacing-sm;
          row-gap: 1rem;
          max-width: 280px;
       }
        .cta-export-type {
          flex-basis: calc(50% - #{$spacing-sm / 2});
        }
        
   }

   // Стили для CTA карточки в сетке продуктов на мобильных устройствах
   .product-card-cta {
     @media (max-width: $breakpoint-lg) {
       background-color: $slate-ultralight;
       border: 2px solid $border-color;
       padding: $spacing-xl;
       min-height: 250px;
       display: flex;
       flex-direction: column;
       justify-content: center;

       .cta-main-title {
         font-size: $font-size-2xl;
         margin-bottom: $spacing-md;
       }

       .cta-description {
         font-size: $font-size-base;
         margin-bottom: $spacing-lg;
       }

       .cta-export-types {
         max-width: 280px;
         margin: 0 auto $spacing-lg;
       }

       .cta-hint {
         font-size: $font-size-sm;
         color: $dark-text;
       }
     }
   }
}