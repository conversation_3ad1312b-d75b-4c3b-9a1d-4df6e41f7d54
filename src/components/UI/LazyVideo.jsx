import React, { useRef, useEffect, useState, memo } from 'react';
import Loader from './Loader';
import './LazyVideo.scss';

// Конфигурация для видео в каталоге
const VIDEO_CONFIG = {
  preload: 'metadata',    // Загружать только метаданные сначала
  playbackRate: 1.0,      // Скорость воспроизведения
  muted: true,            // Без звука для автовоспроизведения
  loop: true,             // Зациклить воспроизведение
  playsInline: true,      // Воспроизводить в лайне (для мобильных устройств)
  loadingThreshold: 0.5,  // Порог видимости для загрузки
  observerMargin: '100px', // Маржин для наблюдателя
};

const LazyVideo = memo(({ src, poster, alt }) => {
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [loadProgress, setLoadProgress] = useState(0);

  // Оптимизированный IntersectionObserver
  useEffect(() => {
    // Создаем IntersectionObserver только один раз
    const options = {
      root: null,
      rootMargin: VIDEO_CONFIG.observerMargin,
      threshold: VIDEO_CONFIG.loadingThreshold,
    };

    // Обработчик пересечения
    const handleIntersection = (entries) => {
      const entry = entries[0]; // Берем только первый элемент для оптимизации
      if (entry && entry.isIntersecting) {
        setIsInView(true);
      }
    };

    const observer = new IntersectionObserver(handleIntersection, options);
    const currentContainer = containerRef.current;

    if (currentContainer) {
      observer.observe(currentContainer);
    }

    return () => {
      if (currentContainer) {
        observer.unobserve(currentContainer);
        observer.disconnect(); // Полностью отключаем наблюдатель
      }
    };
  }, []);

  useEffect(() => {
    if (!isInView || !videoRef.current) return;

    const video = videoRef.current;

    // Обработчики событий
    const handleCanPlay = () => {
      setIsLoading(false);
      video.play().catch((error) => {
        console.error('Autoplay failed:', error);
        setHasError(true);
      });
    };

    const handlePlaying = () => {
      setIsPlaying(true);
    };

    const handleError = (e) => {
      console.warn('Video error:', e);
      setIsLoading(false);
      setHasError(true);
    };

    // Оптимизированный обработчик прогресса загрузки с throttle
    let lastUpdateTime = 0;
    const handleProgress = () => {
      // Обновляем прогресс не чаще чем раз в 300мс для оптимизации
      const now = Date.now();
      if (now - lastUpdateTime < 300) return;
      lastUpdateTime = now;

      // Расчет прогресса загрузки
      if (video.buffered.length > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        if (duration > 0) {
          setLoadProgress((bufferedEnd / duration) * 100);
        }
      }
    };

    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('playing', handlePlaying);
    video.addEventListener('error', handleError);
    video.addEventListener('progress', handleProgress);
    video.addEventListener('loadedmetadata', handleProgress);

    // Настройка видео для стриминга
    video.preload = VIDEO_CONFIG.preload;
    video.muted = VIDEO_CONFIG.muted;
    video.loop = VIDEO_CONFIG.loop;
    video.playsInline = VIDEO_CONFIG.playsInline;
    video.playbackRate = VIDEO_CONFIG.playbackRate;

    // Предзагрузка видео
    video.load();

    return () => {
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('playing', handlePlaying);
      video.removeEventListener('error', handleError);
      video.removeEventListener('progress', handleProgress);
      video.removeEventListener('loadedmetadata', handleProgress);
    };
  }, [isInView]);

  // Оптимизированный рендеринг с мемоизацией URL
  const posterUrl = poster ? poster.replace(/\.(jpg|jpeg|png)$/i, '.webp') : undefined;

  return (
    <div className="lazy-video-container" ref={containerRef}>
      {isLoading && (
        <div className="video-loading">
          <Loader type="shimmer" />
          {loadProgress > 0 && (
            <div className="video-progress">
              <div
                className="video-progress-bar"
                style={{ width: `${loadProgress}%` }}
              ></div>
            </div>
          )}
        </div>
      )}
      {hasError && (
        <div className="video-error">
          <span className="video-error-icon">!</span>
          <span className="video-error-text">Ошибка загрузки видео</span>
        </div>
      )}
      <video
        ref={videoRef}
        className={`lazy-video ${isPlaying ? 'playing' : ''}`}
        muted
        loop
        playsInline
        poster={posterUrl}
        preload="metadata"
      >
        {/* Только MP4 источник для каталога товаров */}
        <source src={src} type="video/mp4" />
        {alt}
      </video>
    </div>
  );
}, (prevProps, nextProps) => {
  // Функция сравнения для предотвращения лишних перерисовок
  return prevProps.src === nextProps.src && prevProps.alt === nextProps.alt;
});

export default LazyVideo;
