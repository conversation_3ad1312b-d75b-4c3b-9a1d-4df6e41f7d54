import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { ym } from 'react-metrika';
import Button from './Button';
import { X, Check, AlertCircle } from 'lucide-react';
import russiaFlag from '../../assets/russia-flag.svg';
// Импорт sendLeadEvent удален
import { getUtmParams } from '../../utils/urlParams';
import { sendLead } from '../../utils/leadService';
import './QuizModal.scss';

const QuizModal = ({ isOpen, onClose, showFullQuiz = true, formData: initialFormData = {} }) => {
  // Удалили ref для IMaskInput
  // Определяем вопросы квиза до их использования
  const questions = [
    {
      title: 'Ваш текущий статус закупки?',
      type: 'radio',
      name: 'status',
      options: [
        { value: 'current', label: 'Уже открыта закупка, нужен актуальный каталог/условия' },
        { value: 'planning', label: 'Планирую открыть закупку в течение 1-2 недель' },
        { value: 'researching', label: 'Собираю информацию, сравниваю поставщиков' },
        { value: 'replacing', label: 'Ищу замену текущему поставщику верхней одежды' },
      ],
    },
    {
      title: 'Как вы предпочитаете выгружать товары для анонса?',
      type: 'radio',
      name: 'exportMethod',
      options: [
        { value: 'manual', label: 'Вручную (копирую фото/текст с сайта)' },
        { value: 'parser', label: 'Использую парсеры (Q-parser, CloudParser)' },
        { value: 'social', label: 'Через VK/OK выгрузку (альбомы)' },
        { value: 'xml', label: 'Нужна XML/YML выгрузка для сайта/платформы' },
        { value: 'undecided', label: 'Пока не определился(ась)' },
      ],
    },
    {
      title: 'Какие категории для вас наиболее приоритетны в БЛИЖАЙШЕЙ закупке?',
      type: 'checkbox',
      name: 'categories',
      options: [
        { value: 'women-spring', label: 'Куртки/ветровки' },
        { value: 'women-coats', label: 'Пальто/плащи' },
        { value: 'men-jackets', label: 'Горнолыжные/спортивные костюмы' },
        { value: 'kids', label: 'Детская/подростковая одежда' },
        { value: 'plus-size', label: 'Большие размеры (баталы)' },
        { value: 'trending', label: 'Хочу увидеть самые ходовые модели' },
      ],
      maxSelect: 3,
    },
    {
      title: 'Какова средняя сумма, на которую вы закрываете сборы?',
      type: 'radio',
      name: 'budget',
      options: [
        { value: 'small', label: 'До 25 000 ₽ (Стартую или небольшие объемы)' },
        { value: 'medium', label: '25 000 ₽ - 70 000 ₽ (Регулярные средние закупки)' },
        { value: 'large', label: '70 000 ₽ - 150 000 ₽ (Стабильно крупные закупки)' },
        { value: 'xlarge', label: 'Более 150 000 ₽ (Очень крупные объемы)' },
      ],
    },
  ];

  // Добавляем -1 как "нулевой этап" для начального экрана квиза
  const [currentStep, setCurrentStep] = useState(showFullQuiz ? -1 : questions.length);

  // Инициализируем React Hook Form
  const {
    register,
    handleSubmit: hookFormSubmit,
    formState: { errors, isSubmitting, isValid },
    control,
    getValues,
    watch
  } = useForm({
    defaultValues: {
      status: initialFormData.status || '',
      exportMethod: initialFormData.exportMethod || '',
      categories: initialFormData.categories || [],
      budget: initialFormData.budget || '',
      name: initialFormData.name || '',
      phone: initialFormData.phone || '',
      email: initialFormData.email || '',
      agreement: true, // Всегда true, так как чекбокс удален
    },
    mode: "onChange" // Валидация при изменении полей
  });

  // Наблюдаем за изменениями полей формы
  const formData = watch();
  const [submitted, setSubmitted] = useState(false);

  // Состояние для отслеживания, когда показывается форма контактов
  const [isContactFormVisible, setIsContactFormVisible] = useState(currentStep === questions.length);

  // Обновляем состояние isContactFormVisible при изменении currentStep
  useEffect(() => {
    setIsContactFormVisible(currentStep === questions.length);
  }, [currentStep, questions.length]);

  // Сбрасываем состояние при открытии/закрытии модального окна
  useEffect(() => {
    if (isOpen) {
      // Если showFullQuiz = false, сразу показываем финальную форму
      // Иначе показываем нулевой этап
      setCurrentStep(showFullQuiz ? -1 : questions.length);
      setSubmitted(false);

      // Сбрасываем позицию скролла
      setTimeout(() => {
        const contentElement = document.querySelector('.quiz-modal-content');
        if (contentElement) {
          contentElement.scrollTop = 0;
        }
      }, 100);
    }
  }, [isOpen, showFullQuiz]);

  const handleNextStep = () => {
    // Переходим к следующему шагу
    setCurrentStep(currentStep + 1);

    // Сбрасываем позицию скролла
    const contentElement = document.querySelector('.quiz-modal-content');
    if (contentElement) {
      contentElement.scrollTop = 0;
    }
  };

  const handlePrevStep = () => {
    setCurrentStep(currentStep - 1);
    // Сбрасываем позицию скролла
    const contentElement = document.querySelector('.quiz-modal-content');
    if (contentElement) {
      contentElement.scrollTop = 0;
    }
  };

  // Состояние для отслеживания ошибок при отправке формы
  const [submitError, setSubmitError] = useState(null);

  const onSubmit = async (data) => {
    setSubmitError(null);

    try {
      console.log('Начинаем отправку формы');

      // Получаем данные формы из React Hook Form
      const contactData = {
        name: data.name,
        phone: data.phone,
        email: data.email,
      };

      // Получаем UTM-метки и _ym_uid
      const utmParams = getUtmParams();

      // Собираем данные квиза
      const quizData = {};

      if (showFullQuiz) {
        // Добавляем данные из квиза, если он был пройден
        if (data.status) {
          const statusOption = questions[0].options.find(opt => opt.value === data.status);
          quizData['Статус закупки'] = statusOption ? statusOption.label : data.status;
        }

        if (data.exportMethod) {
          const exportOption = questions[1].options.find(opt => opt.value === data.exportMethod);
          quizData['Метод выгрузки'] = exportOption ? exportOption.label : data.exportMethod;
        }

        if (data.categories && data.categories.length > 0) {
          const categoryLabels = data.categories.map(catValue => {
            const catOption = questions[2].options.find(opt => opt.value === catValue);
            return catOption ? catOption.label : catValue;
          });
          quizData['Приоритетные категории'] = categoryLabels.join(', ');
        }

        if (data.budget) {
          const budgetOption = questions[3].options.find(opt => opt.value === data.budget);
          quizData['Бюджет закупки'] = budgetOption ? budgetOption.label : data.budget;
        }
      }

      console.log('Подготовленные данные:', { contactData, quizData });

      // Отправляем данные через сервис leadService
      try {
        const result = await sendLead(contactData, quizData, utmParams);
        console.log('Результат отправки:', result);

        if (result && result.success) {
          console.log('Заявка успешно отправлена:', result);

          // Если успешно, отправляем событие и показываем страницу успеха

          // Отправляем события в Яндекс Метрику
          try {
            ym(101165245,'reachGoal','registrationFirstStep');
            ym(32094161,'reachGoal','registrationFirstStep');
            console.log('Отправлены события в Яндекс Метрику: registrationFirstStep');
          } catch (error) {
            console.warn('Ошибка при отправке событий в Яндекс Метрику:', error);
          }

          setSubmitted(true);
        } else {
          // Если есть ошибка, показываем её пользователю
          const errorMsg = result && result.error ? result.error : 'Неизвестная ошибка';
          console.error('Ошибка при отправке заявки:', errorMsg);

          // Просто показываем сообщение об ошибке без дополнительного текста
          setSubmitError(errorMsg);
        }
      } catch (error) {
        console.error('Ошибка при отправке заявки:', error);
        setSubmitError(`Произошла ошибка при отправке данных: ${error.message}. Пожалуйста, попробуйте позже.`);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitError('Не удалось отправить данные. Пожалуйста, попробуйте позже.');
    }
  };



  const modalVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      y: 50,
      transition: {
        duration: 0.2,
        ease: 'easeIn',
      },
    },
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.2,
      },
    },
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        delay: 0.1,
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.2,
      },
    },
  };

  const progressPercentage = currentStep >= 0 ? ((currentStep + 1) / (questions.length + 1)) * 100 : 0;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="quiz-modal-overlay"
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <motion.div
          className="quiz-modal"
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          <button className="quiz-modal-close" onClick={onClose}>
            <X size={24} />
          </button>

          {currentStep >= 0 && !isContactFormVisible && (
            <div className="quiz-modal-header">
              {showFullQuiz && currentStep >= 0 && (
                <div className="quiz-modal-progress">
                  <div className="quiz-modal-progress-bar">
                    <div
                      className="quiz-modal-progress-fill"
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                  <div className="quiz-modal-progress-text">
                    Шаг {currentStep + 1} из {questions.length + 1}
                  </div>
                </div>
              )}
            </div>
          )}

          <div
            className="quiz-modal-content"
            style={{
              borderTop: isContactFormVisible ? 'none' : '1px solid var(--border-color)'
            }}
          >
            <AnimatePresence mode="wait">
              {!submitted ? (
                currentStep === -1 ? (
                  <motion.div
                    key="intro"
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    className="quiz-modal-intro"
                  >
                    <h2 className="quiz-modal-intro-title">Получите<br></br>10 000 ₽</h2>
                    <p className="quiz-modal-intro-subtitle">Ответьте на 4 вопроса и получите подарок на бонусный счет</p>

                    <div className="quiz-modal-intro-benefits">
                      <div className="quiz-modal-intro-benefit">
                        <div className="benefit-icon-wrapper">
                          <Check size={20} strokeWidth={3} className="benefit-icon" />
                        </div>
                        <span>Займет меньше 2 минут</span>
                      </div>
                      <div className="quiz-modal-intro-benefit">
                        <div className="benefit-icon-wrapper">
                          <Check size={20} strokeWidth={3} className="benefit-icon" />
                        </div>
                        <span>Получите доступ к полному каталогу</span>
                      </div>
                      <div className="quiz-modal-intro-benefit">
                        <div className="benefit-icon-wrapper">
                          <Check size={20} strokeWidth={3} className="benefit-icon" />
                        </div>
                        <span>Гарантированный подарок 10 000 ₽</span>
                      </div>
                    </div>

                    <Button
                      variant="primary"
                      size="large"
                      onClick={() => setCurrentStep(0)}
                      className="quiz-modal-intro-button"
                    >
                      Начать
                    </Button>
                  </motion.div>
                ) : currentStep < questions.length ? (
                  <motion.div
                    key={`question-${currentStep}`}
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    className="quiz-modal-question"
                  >
                    <h3 className="quiz-modal-question-title">{questions[currentStep].title}</h3>
                    <div className="quiz-modal-options">
                      {questions[currentStep].options.map((option, index) => (
                        <div className="quiz-modal-option" key={index}>
                          {questions[currentStep].type === 'checkbox' ? (
                            <Controller
                              name="categories"
                              control={control}
                              render={({ field }) => (
                                <>
                                  <input
                                    type="checkbox"
                                    id={`modal-option-${index}`}
                                    value={option.value}
                                    checked={field.value.includes(option.value)}
                                    onChange={(e) => {
                                      const checked = e.target.checked;
                                      const currentValues = [...field.value];
                                      if (checked) {
                                        field.onChange([...currentValues, option.value]);
                                      } else {
                                        field.onChange(currentValues.filter(value => value !== option.value));
                                      }
                                    }}
                                    className="quiz-modal-option-input"
                                  />
                                  <label htmlFor={`modal-option-${index}`} className="quiz-modal-option-label">
                                    {option.label}
                                  </label>
                                </>
                              )}
                            />
                          ) : (
                            <Controller
                              name={questions[currentStep].name}
                              control={control}
                              render={({ field }) => (
                                <>
                                  <input
                                    type="radio"
                                    id={`modal-option-${index}`}
                                    value={option.value}
                                    checked={field.value === option.value}
                                    onChange={() => field.onChange(option.value)}
                                    className="quiz-modal-option-input"
                                  />
                                  <label htmlFor={`modal-option-${index}`} className="quiz-modal-option-label">
                                    {option.label}
                                  </label>
                                </>
                              )}
                            />
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="quiz-modal-actions">
                      {currentStep > 0 && (
                        <Button variant="outline" size="medium" onClick={handlePrevStep}>
                          Назад
                        </Button>
                      )}
                      <Button
                        variant="primary"
                        size="medium"
                        onClick={() => {
                          // Проверяем значения полей из React Hook Form
                          const currentQuestion = questions[currentStep];
                          const currentValue = getValues(currentQuestion.name);

                          if (currentQuestion.type === 'checkbox') {
                            // Для чекбоксов проверяем, что выбран хотя бы один вариант
                            if (currentValue && currentValue.length > 0) {
                              handleNextStep();
                            }
                          } else {
                            // Для радио-кнопок проверяем, что выбран какой-то вариант
                            if (currentValue) {
                              handleNextStep();
                            }
                          }
                        }}
                      >
                        Далее
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="contact-form"
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    className="quiz-modal-contact-form"
                  >
                    <h3 className="quiz-modal-form-title">
                      Зарегистрируйтесь и получите:
                    </h3>

                    <ul className="quiz-modal-benefits-list">
                      <li className="quiz-modal-benefit-item">
                        <div className="benefit-icon-wrapper">
                          <Check size={16} strokeWidth={3} className="benefit-icon" />
                        </div>
                        <div className="benefit-text">
                          <span className="benefit-highlight">Доступ к полному</span>
                          <span>каталогу одежды</span>
                        </div>
                      </li>
                      <li className="quiz-modal-benefit-item">
                        <div className="benefit-icon-wrapper">
                          <Check size={16} strokeWidth={3} className="benefit-icon" />
                        </div>
                        <div className="benefit-text">
                          <span className="benefit-highlight">10 000 ₽</span>
                          <span>на бонусный счёт</span>
                        </div>
                      </li>
                      <li className="quiz-modal-benefit-item">
                        <div className="benefit-icon-wrapper">
                          <Check size={16} strokeWidth={3} className="benefit-icon" />
                        </div>
                        <div className="benefit-text">
                          <span className="benefit-highlight">Удобная</span>
                          <span>автовыгрузка</span>
                        </div>
                      </li>
                    </ul>
                    <form id="contact-form" className="quiz-modal-form" onSubmit={hookFormSubmit(onSubmit)}>
                      {submitError && (
                        <div className="form-error-message">
                          <AlertCircle size={16} />
                          <span>{submitError}</span>
                        </div>
                      )}

                      <div className="form-group">
                        <label htmlFor="modal-name" className="form-label">
                          Имя <span className="required-field">*</span>
                        </label>
                        <div className="input-wrapper">
                          <input
                            type="text"
                            id="modal-name"
                            className={`form-input ${errors.name ? 'input-error' : ''}`}
                            placeholder="Введите ваше имя"
                            {...register("name", { required: true })}
                          />

                        </div>
                      </div>
                      <div className="form-group">
                        <label htmlFor="modal-phone" className="form-label">
                          Телефон <span className="required-field">*</span>
                        </label>
                        <div className="input-wrapper phone-input-wrapper">
                          <div className="phone-prefix">
                            <img src={russiaFlag} alt="Russia" className="country-flag" />
                            <span>+7</span>
                          </div>
                          <input
                            id="modal-phone"
                            className={`form-input phone-input ${errors.phone ? 'input-error' : ''}`}
                            placeholder="(968) 787 82 22"
                            type="tel"
                            inputMode="tel"
                            {...register("phone", {
                              required: true,
                              pattern: /[\d\s\(\)\-]+/
                            })}
                          />

                        </div>
                      </div>
                      <div className="form-group">
                        <label htmlFor="modal-email" className="form-label">
                          E-mail <span className="required-field">*</span>
                        </label>
                        <div className="input-wrapper">
                          <input
                            type="email"
                            id="modal-email"
                            className={`form-input ${errors.email ? 'input-error' : ''}`}
                            placeholder="<EMAIL>"
                            {...register("email", {
                              required: true,
                              pattern: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i
                            })}
                          />

                        </div>
                      </div>

                      <div className="quiz-modal-form-actions">
                        {showFullQuiz && (
                          <Button variant="outline" size="medium" onClick={handlePrevStep}>
                            Назад
                          </Button>
                        )}

                        <Button
                          variant="primary"
                          size="large"
                          type="submit"
                          disabled={isSubmitting || !isValid}
                          className="submit-button"
                          title={!isValid ? "Заполните все обязательные поля" : ""}
                        >
                          {isSubmitting ? 'Отправка...' : 'Зарегистрироваться сейчас'}
                        </Button>
                      </div>
                    </form>
                  </motion.div>
                )
              ) : (
                <motion.div
                  key="success"
                  variants={contentVariants}
                  initial="hidden"
                  animate="visible"
                  className="quiz-modal-success"
                >
                  <div className="success-icon">
                    <Check size={40} strokeWidth={3} />
                  </div>
                  <h3 className="success-title">Спасибо за регистрацию!</h3>
                  <p className="success-message">
                    На вашу почту <strong>{formData.email}</strong> отправлены ссылки на парсеры для скачивания каталога одежды и инструкция, <strong>как сразу получить 10 000 ₽ на бонусный счёт.</strong>
                  </p>
                  <a
                    href={`https://${formData.email.split('@')[1]}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="check-email-button"
                  >
                    Проверить почту
                  </a>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default QuizModal;
