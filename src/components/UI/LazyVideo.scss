@use '../../styles/variables' as *;

.lazy-video-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: rgba($slate, 0.05);

  .lazy-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
    transform: scale(1.02); // Небольшой масштаб для эффекта появления

    &.playing {
      opacity: 1;
      transform: scale(1); // Возвращаем к нормальному размеру при воспроизведении
    }
  }

  .video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;
    background-color: rgba($white, 0.9);
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease-out;

    // Добавляем анимацию появления
    animation: fadeIn 0.3s ease-in-out;

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  }

  .video-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba($slate, 0.05);
    color: $slate;
    z-index: 2;

    &-icon {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: $spacing-sm;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba($slate, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-text {
      font-size: $font-size-sm;
      text-align: center;
    }
  }
}
