import React from 'react';
import './CircularProgressLoader.scss';

const CircularProgressLoader = ({ progress = 0, size = 64, strokeWidth = 4 }) => {
  // Проверяем, что progress - это число и не NaN
  const validProgress = typeof progress === 'number' && !isNaN(progress) ? progress : 0;

  // Расчет параметров для SVG
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDashoffset = circumference - (validProgress / 100) * circumference;

  return (
    <div className="circular-progress-loader">
      <svg
        className="circular-progress-svg"
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
      >
        {/* Фоновый круг */}
        <circle
          className="circular-progress-background"
          cx={size / 2}
          cy={size / 2}
          r={radius}
        />
        {/* Прогресс */}
        <circle
          className="circular-progress-indicator"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          style={{
            strokeDasharray: circumference,
            strokeDashoffset: strokeDashoffset
          }}
        />
      </svg>
      {/* Текст с процентами */}
      <div className="circular-progress-text">
        {Math.round(validProgress)}%
      </div>
    </div>
  );
};

export default CircularProgressLoader;
