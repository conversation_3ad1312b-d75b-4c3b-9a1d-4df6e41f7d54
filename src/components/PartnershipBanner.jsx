import React from 'react';
import { Check } from 'lucide-react';
import Button from './UI/Button';
// Импорт sendLeadEvent удален
import './PartnershipBanner.scss';


const PartnershipBanner = () => {
  const benefits = [
    'Организаторам СП с опытом и без',
    'Тем, кто ищет надёжного поставщика без пересортов и брака',
    'Тем, кто устал от задержек и нестабильного сервиса',
    'Тем, кому важен ассортимент, который действительно покупают'
  ];

  const handleButtonClick = () => {
    if (window.openQuizModal) {
      // Открываем модальное окно с указанием источника, но не отправляем событие сразу
      window.openQuizModal(false, 'partnership_banner');
    }
  };

  return (
    <section className="partnership-banner-section">
      <div className="container">
        <div className="partnership-banner">
          <div className="partnership-banner-image-container">
            <div className="partnership-banner-image-wrapper">
              <div className="partnership-banner-image">
                <picture>
                  <source srcSet="/images/organizer.webp" type="image/webp" />
                  <img
                    src="/images/organizer.jpg"
                    alt="Совместные закупки с МТФОРС"
                    width="600"
                    height="513"
                    loading="eager"
                  />
                </picture>
              </div>

            </div>
          </div>
          <div className="partnership-banner-content">
            <h2 className="partnership-banner-title">
              Для кого подойдут совместные закупки с МТФОРС?
            </h2>
            <ul className="partnership-banner-benefits">
              {benefits.map((benefit, index) => (
                <li key={index} className="partnership-banner-benefit-item">
                  <Check size={20} className="benefit-check-icon" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
            <div className="partnership-banner-cta">
              <Button
                variant="primary"
                size="large"
                onClick={handleButtonClick}
               
              >
                Зарегистрироватся! 
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnershipBanner;
