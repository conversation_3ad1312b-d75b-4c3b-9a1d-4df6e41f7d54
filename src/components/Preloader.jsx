import { useEffect } from 'react';
import { useAppContext } from '../contexts/AppContext';

/**
 * Компонент для управления прелоадером с прогрессивной загрузкой
 * Скрывает прелоадер после загрузки критических компонентов
 */
const Preloader = () => {
  const { isLoading } = useAppContext();

  // Эффект для управления видимостью прелоадера
  useEffect(() => {
    console.log('Preloader: состояние загрузки изменилось', isLoading);

    // Скрываем прелоадер после загрузки критических ресурсов
    if (!isLoading.critical) {
      console.log('Preloader: критические ресурсы загружены, скрываем прелоадер');

      const preloader = document.getElementById('preloader');
      if (preloader) {
        console.log('Скрываем прелоадер после загрузки критических ресурсов');
        preloader.classList.add('preloader-hidden');

        // Удаляем прелоадер из DOM после завершения анимации
        preloader.addEventListener('transitionend', (event) => {
          if (event.propertyName === 'opacity' && preloader.parentNode) {
            preloader.parentNode.removeChild(preloader);
          }
        });

        // Запасной вариант, если событие transitionend не сработает
        setTimeout(() => {
          if (preloader && preloader.parentNode) {
            preloader.parentNode.removeChild(preloader);
          }
        }, 600); // 500ms (время transition) + 100ms запас
      }

      // Показываем контент
      const rootElement = document.getElementById('root');
      if (rootElement) {
        rootElement.classList.add('visible');
      }
    }
  }, [isLoading.critical]);

  // Компонент не рендерит никакой UI
  return null;
};

export default Preloader;
