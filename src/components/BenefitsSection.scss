@import '../styles/variables.scss';

.benefits-section {
 padding: $spacing-2xl 0;
  background-color: $white;



  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 $spacing-md;
  }

  // Используем глобальные стили для заголовков секций

  .benefits-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: $spacing-lg;

    @media (min-width: $breakpoint-md) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .benefit-card {
    background-color: $slate-ultralight;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    box-shadow: $shadow-sm;
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-5px);
      box-shadow: $shadow-md;
    }

    &-large {
      @media (min-width: $breakpoint-md) {
        grid-column: span 2;
      }
    }

    &-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba($blue, 0.05) 0%, rgba($white, 1) 100%);
      z-index: 0;
    }

    &-content {
      position: relative;
      z-index: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .benefit-header {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-md;
    position: relative;
    z-index: 1;
    
   
  }

  .benefit-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: $spacing-sm;
    color: $white;
    flex-shrink: 0;

    &-blue {
      background-color: $blue;
    }

    &-orange {
      background-color: $orange;
    }

    &-green {
      background-color: $green;
    }

    &-pink {
      background-color: $pink;
    }

    &-red {
      background-color: $red;
    }
  }

  .benefit-title {
    font-size: $font-size-lg;
    font-weight: 700;
    color: $dark-text;
    margin: 0;
  }

  .benefit-description {
    font-size: $font-size-base;
    color: $text-color;
    margin-bottom: $spacing-md;
    flex-grow: 1;
    position: relative;
    z-index: 1;
  }

  .benefit-badges {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-xs;
    position: relative;
    z-index: 1;
  }

  .benefit-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background-color: rgba($blue, 0.1);
    color: $blue;
    border-radius: 20px;
    font-size: $font-size-sm;
    font-weight: 600;

    &-icon {
      margin-right: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
    }

    &-green {
      background-color: rgba($green, 0.1);
      color: $green;
    }

    &-orange {
      background-color: rgba($orange, 0.1);
      color: $orange;
    }

    &-pink {
      background-color: rgba($pink, 0.1);
      color: $pink;
    }

    &-red {
      background-color: rgba($red, 0.1);
      color: $red;
    }
  }

  .benefit-example {
    background-color: rgba($red, 0.05);
    border-radius: $border-radius-sm;
    padding: $spacing-md;
    margin-bottom: $spacing-md;
    position: relative;
    z-index: 1;

    &-header {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-xs;
    }

    &-icon {
      margin-right: $spacing-xs;
    }

    &-title {
      font-weight: 600;
      color: $dark-text;
    }

    &-text {
      font-size: $font-size-sm;
      color: $text-color;
      margin: 0;
    }
  }

  // Декоративные элементы для большой карточки
  .benefit-card-large {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -40px;
      right: -40px;
      width: 160px;
      height: 160px;
      background-color: rgba($red, 0.05);
      border-radius: 50%;
      z-index: 0;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -60px;
      left: -60px;
      width: 200px;
      height: 200px;
      background-color: rgba($red, 0.05);
      border-radius: 50%;
      z-index: 0;
    }
  }

  // Стили для кнопки "Показать все преимущества"
  .benefits-show-more {
    display: none;
    text-align: center;
    margin-top: $spacing-xl;

    &-button {
      background-color: $dark-gray;
      border: none;
      color: $white;
      padding: $spacing-md $spacing-2xl;
      border-radius: $border-radius-md;
      font-size: $font-size-md;
      font-weight: 600;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: $shadow-sm;
      min-width: 280px;
      height: 56px;

      &:hover {
        background-color: darken($dark-gray, 5%);
        box-shadow: $shadow-md;
      }

      &:active {
        transform: scale(0.98);
      }
    }

    &-icon {
      margin-left: $spacing-sm;
      transition: transform 0.3s ease;
    }
  }

  // Градиент для скрытия преимуществ
  .benefits-gradient-overlay {
    display: none;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
    pointer-events: none;
    z-index: 2;
  }

  // Адаптивность
  @media (max-width: $breakpoint-lg) {

    .benefit-header{
      flex-direction: column;
    }
    .section-title {
      font-size: 2rem;
      margin-bottom: 1rem;
    }

    .section-subtitle {
      font-size: 1.2rem;
      margin-bottom:2rem;
    }

    .benefit-card {
      padding: 1.25rem 0.75rem;
    }

    .benefit-icon {
      width: 40px;
      height: 40px;
       margin-bottom: 0.75rem;
    }

    .benefit-title {
      font-size: 1.2rem;
      text-align: center;
    }

    .benefit-description {
      font-size: 1.2rem;
      text-align: center;
    }

    .benefit-badge {
      font-size: $font-size-xs;
    }
    .benefit-example{
      text-align: center;
    }
    .benefit-example-header{
          text-align: center;
    flex-direction: column;
    }
    .benefit-badges{
      justify-content: center;
    }

    // Стили для мобильной версии
    .benefits-section {
      position: relative;
    }

    .benefits-grid {
      max-height: 850px; // Высота до карточки "Без выкупа рядов"
      overflow: hidden;
      transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1);

      &.expanded {
        max-height: 5000px; // Достаточно большое значение, чтобы вместить все карточки
      }
    }

    .benefits-gradient-overlay {
      display: block;
      height: 250px; // Увеличиваем высоту градиента
      bottom: 50px; // Позиционируем градиент так, чтобы он начинался на карточке "Без выкупа рядов"
      opacity: 1;
      transition: opacity 0.5s ease;

      .benefits-grid.expanded ~ & {
        opacity: 0;
      }
    }

    .benefits-show-more {
      display: flex;
      justify-content: center;
      position: relative;
      z-index: 3;
      margin-top: 0; // Убираем верхний отступ, чтобы кнопка была ближе к градиенту

      .benefits-show-more-icon {
        margin-left: 8px;
        transition: transform 0.3s ease;
      }

      .button:hover .benefits-show-more-icon {
        transform: translateY(2px);
      }
    }
  }
}
