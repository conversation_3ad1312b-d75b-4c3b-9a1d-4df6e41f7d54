import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import Button from './UI/Button';
import QuizModal from './UI/QuizModal';
import './Quiz.scss';

const Quiz = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [isQuizModalOpen, setIsQuizModalOpen] = useState(false);

  const openQuizModal = () => {
    setIsQuizModalOpen(true);
  };

  const closeQuizModal = () => {
    setIsQuizModalOpen(false);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <div className="quiz-container-wrapper" id="quiz" ref={ref}>
      <div className="container">
        <motion.div
          className="quiz-header text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="section-title text-center">Получите подарок 10 000 ₽ на бонусный счёт</h2>
          <p className="section-subtitle">
            Ответьте на 4 вопроса, и мы подготовим предложение, идеально подходящее под специфику вашей работы
          </p>
        </motion.div>

        <motion.div
          className="quiz-container"
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          variants={containerVariants}
        >
          <div className="quiz-cta">
            <Button
              variant="primary"
              size="large"
              onClick={() => window.openQuizModal ? window.openQuizModal(true) : null}
              className="quiz-cta-button"
            >
              Получить подарок 10 000 ₽
            </Button>
          </div>
        </motion.div>
      </div>

      {/* Модальное окно квиза */}
      <QuizModal
        isOpen={isQuizModalOpen}
        onClose={closeQuizModal}
        showFullQuiz={true}
      />
    </div>
  );
};

export default Quiz;
