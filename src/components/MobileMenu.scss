@use '../styles/variables' as *;

.fixed-mobile-nav {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  opacity: 0;
  transform: translateY(-100%);
  transition: all 0.3s ease;
  background-color: $white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  @media (max-width: $breakpoint-lg) {
    display: block;
  }

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .mobile-nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px $spacing-md;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  .mobile-nav-logo {
    height: 40px;
    display: flex;
    align-items: center;

    img {
      height: 100%;
      width: auto;
      max-width: 129px;
      object-fit: contain;
    }
  }

  .mobile-nav-actions {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .download-catalog-button {
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: $blue;
    color: white;
    border: none;
    border-radius: $border-radius-md;
    padding: 8px 12px;
    font-size: $font-size-sm;
    font-weight: 800;
    cursor: pointer;
    height: 40px;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 5%);
    }
  }

  .hamburger-button {
    background-color: $slate;
    color: white;
    border: none;
    cursor: pointer;
    padding: 8px;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border-radius: $border-radius-md;

    &:hover {
      background-color: darken($slate, 5%);
    }
  }
}

.fixed-hamburger {
  display: none;
  position: fixed;
  top: $spacing-md;
  right: $spacing-md;
  z-index: 999;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;

  @media (max-width: $breakpoint-lg) {
    display: block;
  }

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .hamburger-button {
    background-color: $slate; // Непрозрачный фон
    color: white; // Белый цвет иконки
    border: none;
    cursor: pointer;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border-radius: 8px;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($slate-dark, 0.8);
  z-index: 10000; // Еще более высокий z-index, чем у гамбургера
  display: flex;
  justify-content: flex-end;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;

  &-open {
    visibility: visible;
    opacity: 1;
  }

  &-content {
    width: 85%;
    max-width: 360px;
    height: 100%;
    background-color: $white;
    padding: $spacing-lg;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;

    .mobile-menu-open & {
      transform: translateX(0);
    }
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;
  }

  &-logo {
    height: 40px;
    width: auto;
  }

  &-close {
    background: none;
    border: none;
    cursor: pointer;
    color: $slate-dark;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all 0.3s ease;

    &:hover {
      color: $primary-color;
    }
  }

  &-items {
    display: flex;
    flex-direction: column;
    margin-bottom: $spacing-xl;
    flex-grow: 1;
  }

  &-item {
    padding: $spacing-md 0;
    font-size: $font-size-md;
    font-weight: 600;
    color: $slate-dark;
    text-decoration: none;
    border-bottom: 1px solid $slate-light;
    transition: all 0.3s ease;

    &:hover {
      color: $primary-color;
    }
  }

  &-contact {
    margin-top: auto;
  }

  &-hours {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    margin-top: $spacing-md;

    .operator-icon {
      color: #29B24A;
    }

    &-text {
      display: flex;
      flex-direction: column;
      font-size: $font-size-sm;
      line-height: 1.2;

      div:first-child {
        font-weight: 600;
      }

      div:last-child {
        opacity: 0.8;
      }
    }
  }
}
