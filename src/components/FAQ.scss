@use '../styles/variables' as *;

.faq-section {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at 70% 30%, rgba($secondary-color, 0.05) 0%, transparent 70%);
    z-index: 0;
  }
}

.section-header {
  position: relative;
  z-index: 1;
  margin-bottom: $spacing-2xl;
}

.faq-accordion {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.faq-item {
  background-color: $background-light;
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-md;
  box-shadow: $shadow-md;
  overflow: hidden;
  transition: all $transition-normal;

  &.active {
    box-shadow: $shadow-lg;

    .faq-question {
      background-color: rgba($primary-color, 0.05);

      .faq-icon {
        transform: rotate(180deg);
        color: $primary-color;
      }
    }
  }
}

.faq-question {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg;
  background-color: $background-light;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: $font-size-md;
  font-weight: 600;
  color: $dark-text;
  transition: all $transition-normal;

  &:hover {
    background-color: rgba($primary-color, 0.05);
  }

  span {
    flex: 1;
    padding-right: $spacing-md;
  }
}

.faq-icon {
  color: $text-color;
  transition: all $transition-normal;
  flex-shrink: 0;
  stroke-width: 2px;
}

.faq-answer {
  overflow: hidden;
}

.faq-answer-content {
  padding: 0 $spacing-lg $spacing-lg;
  color: $text-color;
  font-size: $font-size-base;

  @media (min-width: $breakpoint-md) {
    padding: 0 0.75rem 0.75rem;
  }

  p {
    margin-top: 1rem;
    margin-bottom: 1rem;
    @media (max-width: $breakpoint-lg) {
    font-size: 1.2rem;
  }
  }

  h4 {
    font-size: $font-size-md;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: $dark-text;
  }

  ul {
    margin-left: 1.5rem;
    margin-bottom: 1.5rem;

    li {
      margin-bottom: 0.5rem;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -1rem;
        top: 0.5rem;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: $primary-color;
      }
    }
  }
}
