import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ChevronDown } from 'lucide-react';
import './FAQ.scss';

const FAQ = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeIndex, setActiveIndex] = useState(0); // Первый вопрос открыт по умолчанию

  const toggleAccordion = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const faqItems = [
    {
      question: 'Условия акции «Осенний Кешбэк»',
      answer: `
        <p><strong>Возвращайте до 40 000 ₽ с каждой оптовой закупки.</strong> В рамках акции также снижены цены на значительную часть ассортимента, включая новые коллекции.</p>

        <p><strong>Размер кешбэка зависит от суммы заказа:</strong><br>
        15 000 ₽ → кешбэк 500 ₽<br>
        25 000 ₽ → кешбэк 1 000 ₽<br>
        50 000 ₽ → кешбэк 2 500 ₽<br>
        100 000 ₽ → кешбэк 7 500 ₽<br>
        200 000 ₽ → кешбэк 17 500 ₽<br>
        350 000 ₽ → кешбэк 40 000 ₽</p>

        <p>Для заказов от <strong>500 000 ₽</strong> действуют персональные условия: индивидуальный расчет кешбэка, приоритетное обслуживание и эксклюзивные предложения.</p>

        <p><strong>Основные условия:</strong><br>
        • Период проведения: с 1 сентября по 30 ноября<br>
        • Минимальный оптовый заказ: 13 990 ₽<br>
        • Сроки оплаты: 8 дней на выкуп (5 дней на резерв + 3 дня на оплату)<br>
        • Бонус за первый заказ: +1000 ₽, которые необходимо использовать в течение 30 дней<br>
        • Оплата без комиссии: Перевод на расчетный счет, оплата картой или через СБП<br>
        • Документы: Предоставляется полный пакет документов (EAC, ТОРГ-12, Честный знак)</p>

        <p><strong>Дополнительные программы:</strong><br>
        • Бонусная программа MTFORCE: Получите +5% дополнительной прибыли<br>
        • Социальная скидка 3%: Предоставляется для участников СВО, пенсионеров, многодетных семей и инвалидов (требуется удостоверение)</p>

        <p><span style="color: #22c55e; font-weight: bold;">✓</span> <strong>Важное примечание:</strong> Модели одежды из новых коллекций 2025/2026 не продаются на маркетплейсах.</p>
      `,
      isHtml: true, // Флаг для обозначения HTML-контента
    },
    {
      question: 'Работаете ли вы с физ. лицами / самозанятыми организаторами СП?',
      answer: 'Да, мы работаем с физическими лицами, самозанятыми, ИП и ООО. Для каждой формы сотрудничества у нас предусмотрены удобные способы оплаты и документооборота.',
    },
    {
      question: 'Действительно ли можно заказывать без размерных рядов?',
      answer: 'Абсолютно! Вы можете заказывать любые модели и размеры от 1 штуки в рамках минимального заказа. ',
    },
    {
      question: 'Какой актуальный минимальный заказ?',
      answer: 'Стандартный минимальный заказ составляет 24 990 ₽. По акции до 31 мая 2025 года - всего 9 990 ₽. Это отличная возможность начать сотрудничество!',
    },
    {
      question: 'Как быстро происходит отгрузка после оплаты?',
      answer: 'Отгрузка происходит в течение 1-3 рабочих дней после подтверждения оплаты. Часто мы отправляем заказы уже на следующий день. Вы всегда можете отслеживать статус вашего заказа в личном кабинете.',
    },
    {
      question: 'Какие гарантии на товар? Что делать при обнаружении брака?',
      answer: 'В случае обнаружения брака, мы осуществляем обмен или возврат за наш счет в течение 14 дней после получения. Для этого достаточно оформить заявку в личном кабинете и приложить фото брака.',
    },
    {
      question: 'Есть ли у вас API или автоматическая выгрузка каталога?',
      answer: 'Да, мы предоставляем XML/YML выгрузки, готовые решения для популярных парсеров (Q-parser, CloudParser) и социальных сетей. Также доступен API для интеграции с вашими системами. ',
    },
    {
      question: 'Предоставляете ли вы фото/видео для анонсов закупок?',
      answer: 'Да, мы бесплатно предоставляем весь контент: профессиональные фотографии на моделях и видео-обзоры, рекламные баннеры и подробные описания товаров.',
    },
    {
      question: 'Присутствует ли маркировка "Честный ЗНАК" на товарах?',
      answer: ' Мы поставляем только маркированный товар, что защищает вас от штрафов и проблем с контролирующими органами. Вся наша продукция полностью соответствует законодательству РФ.',
    },
  {
      question: 'Возможна ли отсрочка платежа?',
      answer: (
        <>
          <p>Наш партнер HappyLend предлагает онлайн-рассрочку или кредит на следующих условиях:</p>

          <h4>Основные преимущества:</h4>
          <ul>
            <li>Оплачивайте покупку равными частями в течение срока, который выберете сами</li>
            <li>Рассрочка без переплаты: на 6 месяцев</li>
            <li>Кредит с пониженной ставкой: от 2 до 36 месяцев</li>
            <li>Кредит предоставляется в рублях</li>
            <li>Условия рассчитываются индивидуально</li>
            <li>Выбор предложений от нескольких финансовых организаций</li>
            <li>Заполнение анкеты онлайн и одобрение заявки за несколько минут</li>
          </ul>

          <h4>Требования:</h4>
          <ul>
            <li>Гражданство РФ с постоянной регистрацией</li>
            <li>Возраст от 18 лет</li>
            <li>Сумма покупки от 2 000 до 300 000 руб.</li>
            <li>Необходимые документы: паспорт РФ, СНИЛС</li>
          </ul>

          <p>Подать заявку вы можете при оформлении заказа в личном кабинете.</p>
        </>
      ),
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className="faq-section section bg-dark" id="faq">
      <div className="container">
        <motion.div
          className="section-header text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="section-title text-center">Ответы на частые вопросы</h2>
          <p className="section-subtitle">
            Всё, что нужно знать организаторам СП о сотрудничестве с MTFORCE
          </p>
        </motion.div>

        <motion.div
          className="faq-accordion"
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
        >
          {faqItems.map((item, index) => (
            <motion.div
              className={`faq-item ${activeIndex === index ? 'active' : ''}`}
              key={index}
              variants={itemVariants}
            >
              <button
                className="faq-question"
                onClick={() => toggleAccordion(index)}
                aria-expanded={activeIndex === index}
              >
                <span>{item.question}</span>
                <ChevronDown className="faq-icon" size={20} />
              </button>
              <AnimatePresence>
                {activeIndex === index && (
                  <motion.div
                    className="faq-answer"
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="faq-answer-content">
                      {item.isHtml ? (
                        <div dangerouslySetInnerHTML={{ __html: item.answer }} />
                      ) : (
                        <p>{item.answer}</p>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default FAQ;
