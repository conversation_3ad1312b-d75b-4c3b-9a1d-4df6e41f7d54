@use '../styles/variables' as *;

.hero-section {
  overflow: hidden;
  position: relative;
  background-color: transparent;
  color: $light-text;
    padding: 2rem 0;
  @media (min-width: $breakpoint-lg){
padding: 0;
  }

}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
  position: relative;
  z-index: 1;

  @media (min-width: $breakpoint-lg) {
    flex-direction: row;
    align-items: center;
  }
}

.hero-left {
  flex: 1;
  width: 100%;

  @media (min-width: $breakpoint-lg) {
    max-width: 50%;
  }

  @media (max-width: $breakpoint-lg) {
    text-align: center;
  }
}

.hero-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (min-width: $breakpoint-lg) {
    max-width: 50%;
  }
}





.hero-title {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: $spacing-lg;
  color: $dark-text;
  line-height: 1.1;
  position: relative;
  
   @media (max-width: 380px) {
    font-size: 1.8rem;
  }

    @media (min-width: $breakpoint-lg) {
      margin-bottom: $spacing-md;
  }

   @media (min-width: 1100px) {
    font-size: 2.5rem;
  }

}



.hero-subtitle {
  font-size: 1.2rem;
  color: $text-color;

  font-family: $font-family-main;
  line-height: 1.5;
  transition: font-family 0.3s ease-in-out;

  @media (min-width: $breakpoint-lg) {
    max-width: 600px;
  }
}

.trust-info {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-lg;
  margin: 1.5rem 0 2rem 0;
  padding: 1rem 0;
  border-top: 1px solid $slate-light;
  border-bottom: 1px solid $slate-light;
  position: relative;

  @media (max-width: $breakpoint-lg) {
    justify-content: center;
    gap: $spacing-md;
    margin: 2rem 0 2.5rem 0;
    padding:  0;
    border-top: none;
    border-bottom: none;

  }
  &-item:nth-child(3){
    display: none;

    @media (min-width: $breakpoint-xl){
      display: flex;

    }
    @media (max-width: $breakpoint-lg){
      display: flex;

    }

  }


  &-item {
    display: flex;
    align-items: center;
    gap:0.75rem;
    position: relative;

    @media (max-width: $breakpoint-lg) {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: $spacing-xs;
      width: 30%;
    }

  }

  &-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    // Специальные стили для обертки иконки со скидкой
    .discount-item & {
      &::before {
        content: '₽';
        position: absolute;
        top: -8px;
        left: -8px;
        background-color: $green;
        color: $white;
        font-size: 12px;
        font-weight: bold;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid $white;
        z-index: 3; // Выше, чем у блика
      }

      @media (max-width: $breakpoint-lg) {
        &::before {
          top: -5px;
          right: -5px;
          left: auto; // Отменяем left для мобильных
        }
      }
    }
  }

  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background-color: $blue;
    color: $light-text;
    z-index: 1;
    position: relative;
    border-radius: 50%;
    overflow: hidden; // Для скрытия бликов за пределами иконки

    // Адаптивные размеры для мобильных устройств
    @media (max-width: $breakpoint-lg) {
      width: 2.5rem;
      height: 2.5rem;
    }

    // Добавляем стильный блик для всех иконок
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -75%;
      z-index: 2;
      display: block;
      width: 50%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(255,255,255,0) 0%,
        rgba(255,255,255,0.3) 100%
      );
      transform: skewX(-25deg);
      pointer-events: none;
      will-change: transform, opacity; // Оптимизация для анимации
      animation: shineAnimation 3s ease-in-out infinite;
    }
  }

  // Разные задержки для каждой иконки
  &-item:nth-child(1) .trust-info-icon-wrapper .trust-info-icon::after {
    animation-delay: 0.5s; // Небольшая начальная задержка для первой иконки
  }

  &-item:nth-child(2) .trust-info-icon-wrapper .trust-info-icon::after {
    animation-delay: 1.5s; // Задержка 1 секунда после начала анимации первой иконки
  }

  &-item:nth-child(3) .trust-info-icon-wrapper .trust-info-icon::after {
    animation-delay: 2.5s; // Задержка 1 секунда после начала анимации второй иконки
  }



  &-text {
    display: flex;
    flex-direction: column;
    z-index: 1;

    @media (max-width: $breakpoint-lg) {
      text-align: center;
    }
  }

  &-value {
    font-size: $font-size-lg;
    font-weight: 800;
    color: $dark-text;
    line-height: 1.1;

    @media (max-width: $breakpoint-lg) {
      font-size: $font-size-base;
    }
  }

  &-label {
    font-size: $font-size-sm;
    color: $text-color;
    line-height: 1.2;
    margin-top: 2px;

    @media (max-width: $breakpoint-lg) {
      font-size: $font-size-xs;
    }
  }
}





.hero-image {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hero-slideshow {
  width: 100%;
  max-width: 520px;
  max-height: 700px;
  aspect-ratio: 3/4;
  position: relative;

  overflow: hidden;


  background-color: $slate-ultralight;
  transform: skew(-10deg); // Возвращаем наклон для контейнера


  .slide {
    position: absolute;
    top: 0;
    left: -12%; // Смещаем слайд влево
    width: 124%;
    height: 100%;
    overflow: hidden;
    transform: skew(10deg); // Компенсируем наклон контейнера
    will-change: opacity, filter; // Оптимизация производительности

    &:not(:first-child) {
      z-index: 2; // Новый слайд всегда поверх предыдущего
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      transition: transform 8s ease;
      transform: none; // Убираем любые трансформации для фото
    }
  }

  .slide-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: none; // Убираем любые трансформации
    will-change: opacity; // Оптимизация производительности
  }

  .slide-indicators {
    position: absolute;
    bottom: $spacing-md;
    right: $spacing-md;
    display: flex;
    gap: $spacing-sm;
    z-index: 10;
    transform: skew(10deg);

    .indicator {
      width: 8px;
      height: 8px;
      border-radius: 0;
      background-color: rgba($slate, 0.3);
      border: none;
      padding: 0;
      cursor: pointer;
      transition: all $transition-normal;
      transform: skew(-10deg);

      &.active {
        background-color: $blue;
        transform: scale(1.2) skew(-10deg);
      }

      &:hover {
        background-color: rgba($blue, 0.5);
      }
    }
  }
}

// Стили для нового блока с кнопкой и иконками
.hero-cta-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;

  @media (max-width: $breakpoint-lg) {
    width: 100%; // На мобильных на всю ширину
  }
}
.hero-cta-container .button-container {
  width: 100%;
}

.hero-cta {
  @media (min-width: $breakpoint-lg) {
    max-width: 400px;
  }
  @media (min-width: $breakpoint-xl) {
    max-width: 500px;
  }
}
.hero-cta-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%; // Делаем кнопку длиннее
  font-size: $font-size-lg; // Увеличиваем размер текста
  padding: $spacing-md $spacing-lg; // Увеличиваем паддинги

  @media (max-width: $breakpoint-lg) {
    max-width: 100%; // На мобильных на всю ширину
    font-size: $font-size-md; // На мобильных чуть меньше размер текста
  }
}

.hero-export-types {
display: flex;
flex-direction: row;
flex-wrap: wrap;
gap: $spacing-md; /* или просто 1rem; */
justify-content: center; /* Центрируем элементы */
width: fit-content; /* Только ширина, необходимая для содержимого */
margin-top: 1rem;
margin-left: auto;
margin-right: auto;

  @media (max-width: 1100px) {
 display: none;
  }
    @media (max-width: $breakpoint-lg) {
      display: flex;
    margin-top: 1rem;
    gap: $spacing-sm;
  }
}

.hero-export-type {
  display: flex;
  flex-direction: row; // Располагаем иконку и текст в строку
  align-items: center; // Центрируем по вертикали
  gap: $spacing-sm; // Отступ между иконкой и текстом
  width: auto; // Автоматическая ширина
  flex: 0 0 auto; // Запрещаем растягиваться и сжиматься
}

// Стили для обертки иконки
.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px; // Размер 32x32
  height: 32px;
  border-radius: $border-radius-sm;
  transition: all $transition-fast;
  flex-shrink: 0; // Запрещаем сжиматься

  &.icon-color-qparser {
    color: $green;
    background-color: rgba($green, 0.1);
    border: 1px solid rgba($green, 0.2);
  }

  &.icon-color-cloud {
    color: $blue;
    background-color: rgba($blue, 0.1);
    border: 1px solid rgba($blue, 0.2);
  }

  &.icon-color-yml {
    color: $yellow;
    background-color: rgba($yellow, 0.1);
    border: 1px solid rgba($yellow, 0.2);
  }

  &.icon-color-csv {
    color: $slate;
    background-color: rgba($slate, 0.1);
    border: 1px solid rgba($slate, 0.2);
  }

  @media (max-width: $breakpoint-lg) {
    width: 28px; // Немного меньше на мобильных
    height: 28px;
  }
}

.hero-export-icon {
  width: 20px; // Размер иконок 20x20
  height: 20px;
  object-fit: contain;
  display: block; // Чтобы избежать проблем с выравниванием

  @media (max-width: $breakpoint-lg) {
    width: 16px; // Немного меньше на мобильных
    height: 16px;
  }
}

.hero-export-name {
  font-family: $font-family-main;
  font-size: $font-size-xs; // Размер текста
  color: $text-color;
  line-height: 1.2;
  white-space: nowrap;
  text-align: left; // Выравниваем текст по левому краю

  @media (max-width: $breakpoint-lg) {
    font-size: 11px; // Немного меньше на мобильных
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Анимация блика для иконок
@keyframes shineAnimation {
  0%, 10%, 100% {
    left: -75%;
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  35% {
    left: 130%;
    opacity: 1;
  }
  36%, 99% {
    left: -75%;
    opacity: 0;
  }
}

// Медиа-запрос для мобильных устройств
@media (max-width: $breakpoint-lg) {
  @keyframes shineAnimation {
    0%, 10%, 100% {
      left: -75%;
      opacity: 0;
    }
    15% {
      opacity: 1;
    }
    35% {
      left: 110%; // Уменьшаем дистанцию для мобильных устройств
      opacity: 1;
    }
    36%, 99% {
      left: -75%;
      opacity: 0;
    }
  }
}

