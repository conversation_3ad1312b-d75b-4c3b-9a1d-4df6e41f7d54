@use '../styles/variables' as *;

.partnership-steps {
  background-color: $background-light;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($secondary-color, 0.05) 100%);
    z-index: 0;
  }
}

.process-container {
  position: relative;
  padding-left: 3rem;
  max-width: 800px;
  margin: 0 auto;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 5px;
    bottom: 5px;
    width: 2px;
    background-color: $border-color;
  }

  @media (max-width: $breakpoint-sm) {
    padding-left: 2rem;
  }
}

.process-step {
  position: relative;
  margin-bottom: 2.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  &::before {
    content: '';
    position: absolute;
    left: -3rem;
    top: 0.5rem;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: $primary-color;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $white;
    font-weight: 700;
    z-index: 1;

    @media (max-width: $breakpoint-sm) {
      left: -2rem;
      width: 25px;
      height: 25px;
      font-size: 0.8rem;
    }
  }

  &:nth-child(1)::before { content: '1'; }
  &:nth-child(2)::before { content: '2'; }
  &:nth-child(3)::before { content: '3'; }
  &:nth-child(4)::before { content: '4'; }
  &:nth-child(5)::before { content: '5'; }
  &:nth-child(6)::before { content: '6'; }
}

.process-step-title {
  font-size: $font-size-lg;
  font-weight: 700;
  color: $dark-text;
  margin-bottom: $spacing-sm;
}
.partnership-steps .button-container{
  @media (max-width: $breakpoint-lg) {
    width:auto;
  }
}

.process-step-description {
  font-size: $font-size-base;
  color: $text-color;
  margin-bottom: $spacing-md;
  line-height: 1.5;

  p {
    margin-bottom: $spacing-sm;
    @media (max-width: $breakpoint-lg) {
      font-size: 1.2rem;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }

  .process-step-list {
    list-style: none;
    padding-left: 1.5rem;
    margin-bottom: $spacing-sm;

    li {
      position: relative;
      margin-bottom: 0.5rem;

      &::before {
        content: '';
        position: absolute;
        left: -1.2rem;
        top: 0.5rem;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: $primary-color;
      }
    }
  }
}

.process-step-cta {
  margin: $spacing-md 0;
}

.process-step-cta-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-md;

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.process-step-cta-button {
  min-width: 200px;
}

.process-step-export-types {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;

  @media (max-width: $breakpoint-sm) {
    margin-top: $spacing-sm;
  }
}

.process-step-export-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.process-step-export-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  padding: 6px;
  transition: all $transition-fast;

  &.icon-color-api {
    color: $green;
    background-color: rgba($green, 0.07);
    border: 1px solid rgba($green, 0.15);
  }

  &.icon-color-cloud {
    color: $blue;
    background-color: rgba($blue, 0.05);
    border: 1px solid rgba($blue, 0.1);
  }

  &.icon-color-yml {
    color: $yellow;
    background-color: rgba($yellow, 0.07);
    border: 1px solid rgba($yellow, 0.15);
  }

  &.icon-color-csv {
    color: $slate-dark;
    background-color: rgba($slate-dark, 0.05);
    border: 1px solid rgba($slate-dark, 0.1);
  }
}

.process-step-export-name {
  font-size: $font-size-xs;
  color: $text-color;
}

.process-step-badges {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
  margin-top: $spacing-sm;
}

.process-step-badge {
  display: inline-flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-full;
  font-size: $font-size-xs;
  font-weight: 600;

  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    line-height: 1;
  }

  &-primary {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  &-secondary {
    background-color: rgba($secondary-color, 0.1);
    color: $secondary-color;
  }

  &-accent {
    background-color: rgba($accent-color, 0.1);
    color: $accent-color;
  }
}
