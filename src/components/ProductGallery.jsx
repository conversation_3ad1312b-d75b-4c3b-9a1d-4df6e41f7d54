// ProductGallery.jsx
import React, { useState, useEffect, memo, useMemo } from 'react';
import LazyVideo from './UI/LazyVideo';
import CtaCard from './UI/CtaCard';

import CategoryMenu from './UI/CategoryMenu';
import Button from './UI/Button';
import { formatPrice, splitNameIntoTwoLines } from '../utils/productUtils';
// Импорт sendLeadEvent удален
import productsData from '../data/products.json';
import './ProductGallery.scss';

// Упрощенный компонент карточки продукта
const ProductCard = memo(({ category, onClick }) => {
  // Разбиваем имя на строки один раз при рендеринге
  const [firstLine, secondLine] = splitNameIntoTwoLines(category.name);

  // Формируем URL изображения
  const webpSrc = category.imagePath ? category.imagePath.replace(/\.(jpg|jpeg|png)$/i, '.webp') : null;

  return (
    <div className="product-card" onClick={onClick}>
      <div className="product-image">
        {category.imagePath ? (
          <>
            {category.type === 'video' ? (
              <LazyVideo
                src={category.imagePath}
                alt={category.name}
              />
            ) : (
              <picture>
                <source
                  srcSet={webpSrc}
                  type="image/webp"
                />
                <img
                  src={category.imagePath}
                  alt={category.name}
                  className="product-img"
                  loading="lazy"
                  decoding="async"
                  width="600"
                  height="750"
                />
              </picture>
            )}
          </>
        ) : (
          <div className="product-image-error no-image">
            <span className="product-image-error-text">Изображение отсутствует</span>
          </div>
        )}
        {category.imagePath && category.discount && (
          <div className="product-image-labels">
            <span className="product-label product-label-sale">Осенний Кешбэ!
</span>
            <span className="product-label product-label-discount">{category.discount}</span>
          </div>
        )}
      </div>
      <div className="product-info">
        <h3 className="product-name">
          {firstLine}
          {secondLine && <br />}
          {secondLine}
        </h3>
        <div className="product-price-wrapper">
          {category.oldPrice && (
            <div className="product-old-price">{category.oldPrice}</div>
          )}
          <div className="product-price">{category.price}</div>
        </div>
      </div>
    </div>
  );
});

const ProductGallery = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Обработка данных продуктов
  const productCategories = useMemo(() => {
    // Находим индекс CTA карточки
    const ctaIndex = productsData.findIndex(product => product.type === 'cta');

    return productsData.map((product, index) => {
      if (product.type === 'cta') {
        return {
          id: `cta-${index}`,
          name: product.name,
          title: product.title,
          description: product.description,
          buttonText: product.buttonText,
          type: 'cta',
          isCta: true,
        };
      }
      const imagePath = product.image ? `/images/product/${product.image}` : null;
      return {
        id: product.id || `product-${index}`,
        name: product.name,
        price: formatPrice(product.price),
        oldPrice: formatPrice(product.oldPrice),
        discount: product.discount,
        image: product.image,
        imagePath: imagePath,
        type: product.type || 'image',
        // Добавляем флаг для последней карточки перед CTA
        isLastBeforeCta: ctaIndex > 0 && index === ctaIndex - 1,
      };
    });
  }, []);

  // Простой обработчик изменения размера окна
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize, { passive: true });
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Обработчик клика на CTA карточку
  const handleCtaClick = () => {
    // Вызываем форму как в нулевой секции, указывая источник 'catalog'
    if (window.openQuizModal) {
      window.openQuizModal(false, 'catalog');
    }
  };

  // Обработчик клика на карточку товара
  const handleProductClick = () => {
    // Вызываем форму как в нулевой секции, указывая источник 'catalog'
    if (window.openQuizModal) {
      window.openQuizModal(false, 'catalog');
    }
  };

  // Обработчик клика на кнопку "Смотреть все товары"
  const handleViewAllClick = () => {
    // Вызываем форму как в нулевой секции, указывая источник 'catalog'
    if (window.openQuizModal) {
      window.openQuizModal(false, 'catalog');
    }
  };

  return (
    <section className="product-gallery section" id="assortment">
      <div className="container">
        <div className="section-header text-center">
          <h2 className="section-title text-center">Ассортимент, который легко продавать!</h2>
          <p className="section-subtitle">
            Более 1300 качественных и востребованных моделей верхней одежды для успешного проведения СП.
          </p>
        </div>

        <CategoryMenu />

        {/* Плашка с информацией о маркетплейсах */}
        <div className="marketplace-info-banner">
          <div className="marketplace-info-content">
            <div className="marketplace-info-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" fill="#22c55e"/>
                <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <span className="marketplace-info-text">
              Мы не продаем на маркетплейсах модели одежды из новых коллекций 2025/2026
            </span>
          </div>
        </div>

        <div className="product-grid">
          {productCategories.map((category, index) => (
            <div
              key={category.id}
              className={`product-card-wrapper ${category.type === 'cta' ? 'cta-wrapper' : ''} ${category.isLastBeforeCta ? 'last-before-cta' : ''}`}
              style={{
                // Добавляем небольшую задержку для каждой карточки для эффекта каскадной анимации
                animationDelay: `${Math.min(index * 0.05, 0.5)}s`
              }}
            >
              {category.type === 'cta' ? (
                <CtaCard
                  title={category.title}
                  description={category.description}
                  subtitle={category.subtitle}
                  buttonText={category.buttonText}
                  onClick={handleCtaClick}
                />
              ) : (
                <ProductCard
                  category={category}
                  onClick={handleProductClick}
                />
              )}
            </div>
          ))}
        </div>

        {/* Кнопка "Смотреть все модели" */}
        <div className="view-all-container">
          <Button
            variant="primary"
            size="large"
            onClick={handleViewAllClick}
            tooltip="Нажмите «Смотреть все модели», чтобы зарегистрироваться и получить доступ к полному каталогу из 1300+ моделей одежды 👇"
          >
            Смотреть все модели
          </Button>
        </div>
      </div>
    </section>
  );
};

// Мемоизируем весь компонент ProductGallery
export default memo(ProductGallery);