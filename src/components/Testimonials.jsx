import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Quote, Play, Star, X } from 'lucide-react';
import TextTestimonials from './TextTestimonials';
import TestimonialVideo from './UI/TestimonialVideo';
import './Testimonials.scss';

const Testimonials = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.01,
    rootMargin: '0px 0px -10% 0px',
  });

  const isMobile = window.innerWidth < 768;

  const [activeVideo, setActiveVideo] = useState(null);

  const testimonials = [
    {
      id: 1,
      name: 'Ирина Валерьевна',
      role: 'Управляющий директор четырех магазинов',
      text: 'Сотрудничаю с MTFORCE несколько лет и очень довольна. Главное преимущество — широкий выбор стильной одежды для молодежи, высокое качество тканей, отсутствие проблем с размерами и быстрая доставка.',
      rating: 5,
      videoId: 'irina_valerievna',
      thumbnail: 'irina_valerievna.jpg',
    },
    {
      id: 2,
      name: 'Дмитрий',
      role: 'Владелец магазина "Sport Street", г. Балаково',
      text: 'Искал поставщика с привлекательными ценами, высоким качеством и ответственностью. MTFORCE полностью соответствует этим требованиям. Клиенты ценят качество, яркие цвета и доступность. Рекомендую!',
      rating: 5,
      videoId: 'dmitry_owner',
      thumbnail: 'dmitry_owner.jpg',
    },
    {
      id: 3,
      name: 'Снежана Леонидовна',
      role: 'Директор магазина "Стоп-центр", г. Сыктывкар',
      text: 'Недавно начали работать с MTFORCE благодаря привлекательному снижению цен. Заказали разнообразный ассортимент и были приятно удивлены. Отмечаю быструю обработку заказов всей командой и высокое качество продукции, превосходящее ожидания. Рекомендую!',
      rating: 5,
      videoId: 'snezhana_leonidovna',
      thumbnail: 'snezhana_leonidovna.jpg',
    },
  ];

  const handleVideoClick = (videoId) => {
    setActiveVideo(videoId);
  };

  const closeVideo = () => {
    setActiveVideo(null);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: isMobile ? 0.1 : 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: isMobile ? 0.2 : 0.4,
      },
    },
  };

  return (
    <>
      <section className="testimonials-section section" id="testimonials">
        <div className="container">
          <motion.div
            className="section-header text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
            transition={{ duration: isMobile ? 0.2 : 0.4 }}
          >
            <h2 className="section-title text-center">Отзывы наших партнеров</h2>
            <p className="section-subtitle">
            Гордимся тем, что одежда МТФОРС продается не только в крупных розничных сетях, но и в сотнях мелких магазинов, раскинувшихся от Калининграда до Владивостока
            </p>
          </motion.div>

          <motion.div
            className="testimonials-grid"
            ref={ref}
            variants={containerVariants}
            initial="hidden"
            animate={inView ? 'visible' : 'hidden'}
          >
            {testimonials.map((testimonial) => (
              <motion.div
                className="testimonial-card"
                key={testimonial.id}
                variants={itemVariants}
              >
                <div className="testimonial-video-preview" onClick={() => handleVideoClick(testimonial.videoId)}>
                  <div className="testimonial-thumbnail">
                    <div className="testimonial-thumbnail-placeholder"></div>
                    <picture>
                      <source
                        srcSet={`/images/testimonials/${testimonial.thumbnail.replace(/\.(jpg|jpeg|png)$/i, '.webp')}`}
                        type="image/webp"
                      />
                      <img
                        src={`/images/testimonials/${testimonial.thumbnail}`}
                        alt={`Видео-отзыв от ${testimonial.name}`}
                        className="testimonial-thumbnail-img"
                        loading="lazy"
                        width="640"
                        height="360"
                      />
                    </picture>
                    <div className="testimonial-play-button">
                      <Play size={20} />
                    </div>
                  </div>
                </div>
                <div className="testimonial-content">
                  <div className="testimonial-quote">
                    <Quote className="quote-icon" size={24} />
                    <p className="testimonial-text">{testimonial.text}</p>
                  </div>
                  <div className="testimonial-rating">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        size={16}
                        className={i < testimonial.rating ? 'star-filled' : 'star-empty'}
                        fill={i < testimonial.rating ? 'currentColor' : 'none'}
                      />
                    ))}
                  </div>
                  <div className="testimonial-author">
                    <h3 className="author-name">{testimonial.name}</h3>
                    <p className="author-role">{testimonial.role}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {activeVideo && (
            <div className="video-modal" onClick={closeVideo}>
              <div className="video-modal-content" onClick={(e) => e.stopPropagation()}>
                <button className="video-modal-close" onClick={closeVideo}>
                  <X size={18} strokeWidth={2.5} />
                </button>
                <div className="video-container">
                  <TestimonialVideo
                    src={`/images/testimonials/${activeVideo}.mp4`}
                    poster={`/images/testimonials/${activeVideo}.jpg`}
                    alt={testimonials.find(t => t.videoId === activeVideo)?.name || 'Видео-отзыв'}
                  />
                </div>
              </div>
            </div>
          )}

        </div>
      </section>

      {/* Текстовые отзывы */}
      <TextTestimonials />
    </>
  );
};

export default Testimonials;
