@use '../styles/variables' as *;

.header {
  position: relative;
  width: 100%;
  z-index: $z-index-fixed;
  background-color: transparent;
  padding: 1rem 0;
  color: $text-color;
  border-bottom: 1px solid $slate-light;
  @media (max-width: $breakpoint-lg) {
    border-bottom: none;

  }
  &-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: $spacing-sm;

    @media (min-width: $breakpoint-lg) {
      flex-wrap: nowrap;

    }
  }

  &-actions {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    @media (min-width: $breakpoint-lg) {
      gap: $spacing-md;
    }
  }
}

.floating-cta {
  display: none;
  position: fixed;
  top: $spacing-md;
  right: $spacing-md;
  z-index: $z-index-fixed;

  @media (min-width: $breakpoint-lg) {
    display: block;
  }

  .floating-button {
    box-shadow: 0 8px 16px rgba($primary-color, 0.15);
    transform: scale(1.05);
  }
}



.logo-rating-group {
  display: flex;
  align-items: center;
  gap: $spacing-xl;


  @media (max-width: $breakpoint-lg) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: $spacing-xs;
  }
}

.logo {
  z-index: $z-index-fixed + 1;

  a {
    display: flex;
    align-items: center;
    text-decoration: none;
  }

  &-image {
    height: 52px;
    width: auto;
    transition: transform 0.3s ease;

    @media (max-width: $breakpoint-lg) {
      height: 42px;
    }

    &:hover {
      transform: scale(1.05);
    }
  }
}

.trust-badges {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0;
}

.trust-badge {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: $spacing-sm $spacing-sm;
  width: 120px;
  position: relative;
  transform: skew(-10deg);

  @media (max-width: $breakpoint-lg) {
    padding: 0.5rem;
    width: 70px;

  }

  &:nth-child(1) {

    border-right: 1px solid $slate-light;
  }

  &:nth-child(2) {

    border-right: 1px solid $slate-light;
  }



  .rating-label {
    font-size: $font-size-xs;
    color: $text-color;
    text-align: center;
    line-height: 1;
    margin-bottom: 4px;
    opacity: 0.8;
    transform: skew(10deg);

    @media (max-width: $breakpoint-lg) {
      font-size: 9px;
      margin-bottom: 2px;
    }
  }

  .rating-value {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-xs;
    line-height: 1;
    transform: skew(10deg);

    @media (max-width: $breakpoint-lg) {
      gap: 2px;
    }
  }

  .rating-icon {
    height: 20px;
    width: auto;
    transition: transform 0.3s ease;

    @media (max-width: $breakpoint-lg) {
      height: 16px;
    }

    &:hover {
      transform: scale(1.1) skew(0deg);
    }
  }

  &-rating {
    font-size: $font-size-base;
    font-weight: 700;
    color: $rating-text;
    line-height: 1;

    border-radius: $border-radius-sm;
  }

}

.contact-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-md;

  @media (max-width: $breakpoint-lg) {
    display: none;
  }
}

.work-hours {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-sm;
  color: $text-color;
  gap:0.5rem;

  @media (max-width: $breakpoint-xl) {
    display: none;

  }

  .operator-icon {
    color: #29B24A; // Зеленый цвет для иконки оператора
  }

  &-text {
    display: flex;
    flex-direction: column;
    font-size: $font-size-xs;
    line-height: 1.2;

    div:first-child {
      font-weight: 600;
    }

    div:last-child {
      opacity: 0.8;
    }
  }
}

.phone-container {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  @media (max-width: $breakpoint-lg) {
    display: none;
  }
}

.models-info {
  display: flex;
  align-items: flex-start;
  gap: 4px;

  .check-icon {
    color: $slate-light;
    margin-top: 2px;
  }

  .models-text {
    display: flex;
    flex-direction: column;
    font-size: 10px;
    line-height: 1.2;
    color: $light-text;

    span:first-child {
      font-weight: 600;
      color: $slate-light;
    }
  }
}

// Стили для кнопки телефона удалены

/* Навигация удалена */

/* Меню-гамбургер удалено */

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
