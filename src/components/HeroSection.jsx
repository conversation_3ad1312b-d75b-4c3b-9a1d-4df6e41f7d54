import React, { useState, useEffect } from 'react';
import Loader from './UI/Loader';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Package, BicepsFlexed, Handshake, CloudDownload, FileCode, Sheet } from 'lucide-react';
import Button from './UI/Button';
import CountdownTimer from './UI/CountdownTimer';
import qParserIcon from '../assets/Q-parser.svg';
import './HeroSection.scss';

// Import slider images
import slider1 from '/images/slider1.jpg';
import slider2 from '/images/slider2.jpg';
import slider3 from '/images/slider3.jpg';
import slider4 from '/images/slider4.jpg';
import slider5 from '/images/slider5.jpg';
import slider6 from '/images/slider6.jpg';
import slider7 from '/images/slider7.jpg';
import slider8 from '/images/slider8.jpg';

const HeroSection = ({ openQuizModal }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1, // Чуть больший порог для старта анимации
    rootMargin: '0px 0px -10% 0px',
  });

  // Массив с типами экспорта для отображения рядом с кнопкой
  const exportTypes = [
    { iconType: 'custom', iconSrc: qParserIcon, name: 'Q-parser', colorClass: 'icon-color-qparser' },
    { iconType: 'lucide', Icon: CloudDownload, name: 'CloudParser', colorClass: 'icon-color-cloud' },
    { iconType: 'lucide', Icon: FileCode, name: 'YML', colorClass: 'icon-color-yml' },
    { iconType: 'lucide', Icon: Sheet, name: 'CSV', colorClass: 'icon-color-csv' },
  ];

  // State for slideshow
  const [currentSlide, setCurrentSlide] = useState(0);
  const slides = [slider1, slider2, slider3, slider4, slider5, slider6, slider7, slider8];
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  // Предзагрузка первого слайда для улучшения LCP
  const [slidesLoaded, setSlidesLoaded] = useState({ 0: true });
  const [slidesLoading, setSlidesLoading] = useState({});

  // Check if device is mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Preload slide images только для десктопа
  useEffect(() => {
    // Полностью пропускаем загрузку слайдов на мобильных устройствах
    if (isMobile) return;

    // Preload current slide
    if (!slidesLoaded[currentSlide] && !slidesLoading[currentSlide]) {
      setSlidesLoading(prev => ({ ...prev, [currentSlide]: true }));

      // Загружаем слайд без задержки
      setSlidesLoaded(prev => ({ ...prev, [currentSlide]: true }));
      setSlidesLoading(prev => ({ ...prev, [currentSlide]: false }));
    }

    // Preload next slide только если мы на десктопе
    if (!isMobile) {
      const nextSlide = currentSlide === slides.length - 1 ? 0 : currentSlide + 1;
      if (!slidesLoaded[nextSlide] && !slidesLoading[nextSlide]) {
        setSlidesLoading(prev => ({ ...prev, [nextSlide]: true }));

        // Загружаем следующий слайд без задержки
        setSlidesLoaded(prev => ({ ...prev, [nextSlide]: true }));
        setSlidesLoading(prev => ({ ...prev, [nextSlide]: false }));
      }
    }
  }, [currentSlide, isMobile, slides.length, slidesLoaded, slidesLoading]);

  // Auto-advance slideshow only on desktop
  useEffect(() => {
    if (isMobile) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    }, 6000); // Увеличено время между сменой слайдов
    return () => clearInterval(interval);
  }, [slides.length, isMobile]);

  // Оптимизированные варианты анимации для элементов героя
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: (i) => ({ // Используем custom prop 'i' для задержки
      y: 0,
      opacity: 1,
      transition: {
        duration: isMobile ? 0.4 : 0.6, // Быстрее
        ease: [0.22, 1, 0.36, 1], // Более плавная кривая анимации (cubic-bezier)
        delay: i * 0.15 + 0.1 // Меньшая базовая задержка + меньший инкремент
      },
    }),
  };

  // Варианты анимации для заголовка - БЕЗ задержки
  const titleVariants = {
    hidden: { opacity: 1 }, // Сразу видимый, без анимации прозрачности
    visible: {
      opacity: 1,
      transition: {
        duration: 0, // Мгновенное появление
        delay: 0, // Без задержки
      },
    },
  };

  // LCP Element (Subtitle) - БЕЗ анимации для мгновенного появления
  const subtitleVariantsLCP = {
    hidden: { opacity: 1 }, // Сразу видимый, без анимации прозрачности
    visible: {
      opacity: 1,
      transition: {
        duration: 0, // Мгновенное появление
        delay: 0 // Без задержки
      },
    },
  };

  // Анимация для правого блока (слайдшоу) - только для десктопа
  const rightBlockVariants = {
    hidden: { opacity: 0, x: isMobile ? 0 : 60 }, // Больший сдвиг для десктопа, чтобы анимация была более заметной
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: isMobile ? 0.4 : 0.8, // Более длительная анимация для десктопа
        delay: 0.2, // Небольшая задержка, чтобы началась после появления LCP
        ease: [0.22, 1, 0.36, 1] // Более плавная кривая для эффектной анимации
      }
    }
  };

  return (
    <motion.section
      className="hero-section"
      id="hero"
      ref={ref}
      initial="hidden"
      animate={inView ? 'visible' : 'hidden'}
    >
      <div className="container">
        <div className="hero-content">
          <div className="hero-left">
            <motion.h1
              className="hero-title"
              variants={titleVariants}
              // initial и animate наследуются от родителя (motion.section)
            >
              Одежда оптом для организато<span className="mobile-only">-</span><br className="mobile-only" />ров совместных покупок
            </motion.h1>

            <motion.p
              className="hero-subtitle"
              variants={subtitleVariantsLCP} // Используем LCP-оптимизированные варианты
            >
              Честные оптовые цены без посредников и популярность нашей одежды обеспечат вам стабильный оргсбор, а бонусная программа поможет заработать еще больше — сверх основной прибыли.
            </motion.p>

            <motion.div
              className="trust-info"
              variants={itemVariants}
              custom={0} // Первый элемент после LCP (i=0)
            >
              <div className="trust-info-item">
                <div className="trust-info-icon-wrapper">
                  <div className="trust-info-icon">
                    <Package size={24} />
                  </div>
                </div>
                <div className="trust-info-text">
                  <span className="trust-info-value">1300+</span>
                  <span className="trust-info-label">модели одежды</span>
                </div>
              </div>
              <div className="trust-info-item discount-item">
                <div className="trust-info-icon-wrapper">
                  <div className="trust-info-icon">
                    <BicepsFlexed size={24} />
                  </div>
                </div>
                <div className="trust-info-text">
                  <span className="trust-info-value">Кешбэк</span>
                  <span className="trust-info-label">до 40 000₽</span>
                </div>
              </div>
              <div className="trust-info-item">
                <div className="trust-info-icon-wrapper">
                  <div className="trust-info-icon">
                    <Handshake size={24} />
                  </div>
                </div>
                <div className="trust-info-text">
                  <span className="trust-info-value">9 990 ₽</span>
                  <span className="trust-info-label">мин. заказ</span>
                </div>
              </div>
            </motion.div>

            {/* Таймер для мобильных устройств - показывается только на мобильных */}
            <motion.div
              className="mobile-countdown-container"
              variants={itemVariants}
              custom={1}
            >
              <CountdownTimer targetDate="2025-11-30T23:59:59" className="mobile-countdown" />
            </motion.div>

            <motion.div
              className="hero-cta"
              variants={itemVariants}
              custom={1} // Второй элемент после LCP (i=1)
            >
              <div className="hero-cta-container">
                <Button
                  variant="primary"
                  size="large"
                  onClick={openQuizModal}
                  className="hero-cta-button"
                   tooltip="Нажмите «Выгрузить каталог», чтобы зарегистрироваться и получить доступ к каталогу одежды 👇"
                >
                  Выгрузить каталог
                </Button>

                <div className="hero-export-types">
                  {exportTypes.map((item) => (
                    <div key={item.name} className="hero-export-type">
                      {item.iconType === 'custom' ? (
                        <span className={`icon-wrapper ${item.colorClass}`}>
                          <img
                            src={item.iconSrc}
                            alt={item.name}
                            className="hero-export-icon"
                            width="20"
                            height="20"
                          />
                        </span>
                      ) : (
                        <span className={`icon-wrapper ${item.colorClass}`}>
                          <item.Icon
                            size={20}
                            strokeWidth={1.5}
                          />
                        </span>
                      )}
                      <span className="hero-export-name">{item.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="hero-right"
            variants={rightBlockVariants}
          >
            <div className="hero-image">
              {/* Удален блок slideshow-download-button */}
              {!isMobile && (
                <div className="hero-slideshow">
                  <AnimatePresence mode="popLayout" initial={false}>
                    <motion.div
                      key={currentSlide}
                      className="slide"
                      initial={{
                        opacity: 0,
                        zIndex: 2
                      }}
                      animate={{
                        opacity: 1,
                        zIndex: 1
                      }}
                      exit={{
                        opacity: 0,
                        zIndex: 0
                      }}
                      transition={{
                        duration: 1.8,
                        ease: [0.2, 0.0, 0.0, 1.0] // cubic-bezier для максимально плавной анимации
                      }}
                      // Стили перенесены в CSS
                    >
                      {slidesLoaded[currentSlide] ? (
                        <picture>
                          <source
                            srcSet={slides[currentSlide].replace(/\.(jpg|jpeg|png)$/i, '.webp')}
                            type="image/webp"
                          />
                          <img
                            src={slides[currentSlide]}
                            alt="MTFORCE collection"
                            width="900"
                            height="1140"
                            fetchPriority={currentSlide === 0 ? "high" : "auto"} // Высокий приоритет для первого слайда
                            onError={(e) => {
                              // Если WebP не поддерживается или изображение не загрузилось,
                              // логируем ошибку, но не показываем пользователю
                              console.warn(`Ошибка загрузки изображения: ${e.target.src}`);
                            }}
                          />
                        </picture>
                      ) : (
                        <div className="slide-placeholder">
                          {slidesLoading[currentSlide] && <Loader type="shimmer" />}
                        </div>
                      )}
                    </motion.div>
                  </AnimatePresence>

                  <div className="slide-indicators">
                    {slides.map((_, index) => (
                      <button
                        key={index}
                        className={`indicator ${index === currentSlide ? 'active' : ''}`}
                        onClick={() => setCurrentSlide(index)}
                        aria-label={`Go to slide ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
              )}
              {/* На мобильных устройствах не показываем изображения вообще */}
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default HeroSection;
