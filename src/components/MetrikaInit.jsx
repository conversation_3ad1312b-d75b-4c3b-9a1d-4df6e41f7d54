import { MetrikaCounters } from 'react-metrika';

/**
 * Компонент для инициализации Яндекс Метрики
 * Размещается в начале приложения для раннего запуска счетчиков
 *
 * Следует официальным рекомендациям:
 * 1. Инициализация счетчика происходит как можно раньше
 * 2. Используются стандартные значения для отслеживания отказов
 * 3. Отслеживание посещений происходит автоматически при инициализации
 */
const MetrikaInit = () => {
  return (
    <MetrikaCounters
      ids={[101165245, 32094161]}
      options={{
        clickmap: true,
        trackLinks: true,
        accurateTrackBounce: true, // Стандартное время отслеживания отказов (15 секунд)
        webvisor: true,
        trackHash: true, // Важно для SPA-приложений
        captureFormSubmit: true
      }}
    />
  );
};

export default MetrikaInit;
