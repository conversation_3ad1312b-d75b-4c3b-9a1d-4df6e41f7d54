import React, { useState, useEffect } from 'react';
import { Ban, ShieldCheck, CalendarCheck, Truck, Coins, Code, DollarSign, Check, Percent, CreditCard, Equal, Heart, Ruler, ChevronDown } from 'lucide-react';
import Button from './UI/Button';
import './BenefitsSection.scss';

const BenefitsSection = () => {
  const [expanded, setExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Проверяем, является ли устройство мобильным
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Проверяем при загрузке
    checkMobile();

    // Проверяем при изменении размера окна
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Функция для показа всех преимуществ
  const showAllBenefits = () => {
    setExpanded(true);
  };

  return (
    <section id="benefits" className="benefits-section section">
      <div className="container">
        <div className="section-header text-center">
          <h2 className="section-title text-center">Почему организаторы СП <br className="desktop-only" />выбирают MTFORCE</h2>
          <p className="section-subtitle">
            Уже более 10 лет мы помогаем организаторам СП зарабатывать больше, масштабироваться и занимать лидирующие позиции на рынке России. Работая с нами, вы сможете высвободить время и ресурсы, <strong>чтобы сконцентрироваться на главном — эффективном сборе заказов</strong>.
          </p>
        </div>

        <div className={`benefits-grid ${expanded ? 'expanded' : ''}`}>
          {/* Бонусная программа */}
          <div className="benefit-card benefit-card-large">
            <div className="benefit-card-bg"></div>
            <div className="benefit-card-content">
              <div className="benefit-header">
                <div className="benefit-icon benefit-icon-red">
                  <Coins size={24} strokeWidth={2} />
                </div>
                <h3 className="benefit-title">Бонусная программа<br></br>для организаторов СП</h3>
              </div>

              <p className="benefit-description">
               За каждую оптовую покупку на ваш бонусный счёт в личном кабинете зачисляются баллы, которыми вы можете
оплатить до 5% стоимости последующих заказов и увеличить свою чистую прибыль.
              </p>

              <div className="benefit-example">
                <div className="benefit-example-header">
                  <span className="benefit-example-icon">💡</span>
                  <span className="benefit-example-title">Как оплатить заказ баллами?</span>
                </div>
                <p className="benefit-example-text">
                  Накопленные баллы MTFORCE могут покрыть до 5% стоимости оптового заказа, при условии, что его общая сумма соответствует или превышает минимальную сумму заказа.
                </p>
              </div>

              <div className="benefit-badges">
                <span className="benefit-badge benefit-badge-red">
                  <span className="benefit-badge-icon"><Percent size={14} strokeWidth={2.5} /></span> 5% кэшбэк баллами
                </span>
                <span className="benefit-badge benefit-badge-red">
                  <span className="benefit-badge-icon"><CreditCard size={14} strokeWidth={2.5} /></span> Оплата до 10% заказа
                </span>
                <span className="benefit-badge benefit-badge-red">
                  <span className="benefit-badge-icon"><Equal size={14} strokeWidth={2.5} /></span> 1 балл = 1 рубль
                </span>
              </div>
            </div>
          </div>

          {/* Карточка 2 */}
          <div className="benefit-card">
            <div className="benefit-header">
              <div className="benefit-icon benefit-icon-blue">
                <ShieldCheck size={24} strokeWidth={2} />
              </div>
              <h3 className="benefit-title">Гарантия качества</h3>
            </div>

            <p className="benefit-description">
              Собственная фабрика и контроль на всех этапах производства. Вся продукция сертифицирована и промаркирована по системе "Честный ЗНАК".
            </p>

            <div className="benefit-badges">
              <span className="benefit-badge benefit-badge-blue">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Сертифицировано
              </span>
              <span className="benefit-badge benefit-badge-blue">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Честный ЗНАК
              </span>
            </div>
          </div>

          {/* Карточка 3 */}
          <div className="benefit-card">
            <div className="benefit-header">
              <div className="benefit-icon benefit-icon-green">
                <CalendarCheck size={24} strokeWidth={2} />
              </div>
              <h3 className="benefit-title">8 дней на выкуп заказа</h3>
            </div>

            <p className="benefit-description">
              Резервируем выбранные модели одежды на 5 дней бесплатно — вы спокойно собираете заказы и предоплату.
            </p>

            <div className="benefit-badges">
              <span className="benefit-badge benefit-badge-green">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> 5 дней резерв
              </span>
                <span className="benefit-badge benefit-badge-green">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> 72 часа на оплату
              </span>
            </div>
          </div>

          {/* Карточка 4 */}
          <div className="benefit-card">
            <div className="benefit-header">
              <div className="benefit-icon benefit-icon-blue">
                <Truck size={24} strokeWidth={2} />
              </div>
              <h3 className="benefit-title">Оперативная отгрузка</h3>
            </div>

            <p className="benefit-description">
              Отгружаем заказы в течение 1-3 дней после оплаты. Бесплатная доставка в пределах МКАД. Работаем со всеми транспортными компаниями.
            </p>

            <div className="benefit-badges">
              <span className="benefit-badge benefit-badge-blue">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> 1-3 дня
              </span>
            </div>
          </div>

          {/* Карточка 7 - Минимальный заказ */}
          <div className="benefit-card">
            <div className="benefit-header">
              <div className="benefit-icon benefit-icon-red">
                <DollarSign size={24} strokeWidth={2} />
              </div>
              <h3 className="benefit-title">Гибкие <br></br> условия</h3>
            </div>

            <p className="benefit-description">
             Стандартная минимальная сумма заказа 24 990 ₽. Если вы не набираете минимальную сумму, мы готовы обсудить индивидуальные условия сотрудничества.
            </p>

            <div className="benefit-badges">
              <span className="benefit-badge benefit-badge-red">
                <span className="benefit-badge-icon"><DollarSign size={14} strokeWidth={2.5} /></span> Сейчас мин. заказ всего 13 990 ₽

              </span>
            </div>
          </div>

          {/* Без выкупа рядов */}
          <div className="benefit-card benefit-card-large">
            <div className="benefit-card-bg"></div>
            <div className="benefit-card-content">
              <div className="benefit-header">
                <div className="benefit-icon benefit-icon-blue">
                  <Ban size={24} strokeWidth={2} />
                </div>
                <h3 className="benefit-title">Без выкупа рядов</h3>
              </div>

              <p className="benefit-description">
                Заказывайте только то, что нужно, в любых размерах и количествах. Никаких обязательных рядов и ненужных остатков. Ходовые размеры могут стоить немного дороже, а менее популярные — дешевле.
              </p>

              <div className="benefit-badges">
                <span className="benefit-badge benefit-badge-blue">
                  <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Любые артикулы
                </span>
                <span className="benefit-badge benefit-badge-blue">
                  <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Любые размеры
                </span>
                <span className="benefit-badge benefit-badge-blue">
                  <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> От 1 единицы
                </span>
              </div>
            </div>
          </div>

          {/* Карточка 6 - Автовыгрузка */}
          <div className="benefit-card">
            <div className="benefit-header">
              <div className="benefit-icon benefit-icon-orange">
                <Code size={24} strokeWidth={2} />
              </div>
              <h3 className="benefit-title">Автовыгрузка через парсеры</h3>
            </div>

            <p className="benefit-description">
              Создавайте закупки быстрее с помощью автоматической выгрузки товаров. Получайте актуальные данные о наличии, ценах и характеристиках товаров в реальном времени.
            </p>

            <div className="benefit-badges">
              <span className="benefit-badge benefit-badge-orange">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Q-parser
              </span>
              <span className="benefit-badge benefit-badge-orange">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> CloudParser
              </span>
              <span className="benefit-badge benefit-badge-orange">
                <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> YML
              </span>
            </div>
          </div>

          {/* Скидка 3% - Социальная поддержка (узкая карточка слева) */}
          <div className="benefit-card">
            <div className="benefit-header">
              <div className="benefit-icon benefit-icon-pink">
                <Heart size={24} strokeWidth={2} />
              </div>
              <h3 className="benefit-title">Особые скидки</h3>
            </div>

            <p className="benefit-description">
              Предоставляем специальную скидку 3% для участников СВО, пенсионеров, многодетных семей и инвалидов. Для получения скидки достаточно предъявить соответствующее удостоверение.
            </p>

            <div className="benefit-badges">
              <span className="benefit-badge benefit-badge-pink">
                <span className="benefit-badge-icon"><Percent size={14} strokeWidth={2.5} /></span> Скидка 3%
              </span>
              <span className="benefit-badge benefit-badge-pink">
                <span className="benefit-badge-icon"><Heart size={14} strokeWidth={2.5} /></span> Социальная поддержка
              </span>
            </div>
          </div>

          {/* Забудьте о возвратах и претензиях из-за размера (широкая карточка справа) */}
          <div className="benefit-card benefit-card-large">
            <div className="benefit-card-bg"></div>
            <div className="benefit-card-content">
              <div className="benefit-header">
                <div className="benefit-icon benefit-icon-green">
                  <Ruler size={24} strokeWidth={2} />
                </div>
                <h3 className="benefit-title">Детализированные и точные<br></br>таблицы размеров</h3>
              </div>

              <p className="benefit-description">
                Мы тщательно замеряем каждую модель одежды по внешним швам, учитывая значительно больше параметров, чем стандартные сетки (например, до 14 замеров для горнолыжного костюма). Детальные измерения помогают вашим покупателям точно подобрать размер и снижают количество возвратов.
              </p>

              <div className="benefit-badges">
                <span className="benefit-badge benefit-badge-green">
                  <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Замер каждой модели
                </span>
                <span className="benefit-badge benefit-badge-green">
                  <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> До 14 параметров
                </span>
                <span className="benefit-badge benefit-badge-green">
                  <span className="benefit-badge-icon"><Check size={14} strokeWidth={2.5} /></span> Отсутствие возвратов
                </span>
              </div>
            </div>
          </div>
        </div>

        {isMobile && !expanded && (
          <div className="benefits-gradient-overlay"></div>
        )}

        {isMobile && !expanded && (
          <div className="benefits-show-more">
            <Button
              variant="secondary"
              size="large"
              onClick={showAllBenefits}
              className="benefits-show-more-button"
            >
              Показать все
              <ChevronDown
                size={20}
                className="benefits-show-more-icon"
              />
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default BenefitsSection;
