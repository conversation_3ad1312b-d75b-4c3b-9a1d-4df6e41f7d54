import React, { useState, useEffect } from 'react';
import { Menu, X, Phone, Headphones, Download } from 'lucide-react';
import logoMtforce from '../assets/icons/logo-mtforce.svg';
import Button from './UI/Button';
import './MobileMenu.scss';

const MobileMenu = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Обработчик скролла для отображения кнопки гамбургера
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 100;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener('scroll', handleScroll);
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // Функция для переключения мобильного меню
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
    // Блокируем прокрутку страницы при открытом меню
    document.body.style.overflow = !mobileMenuOpen ? 'hidden' : '';
  };

  // Функция для открытия формы регистрации
  const openRegistrationForm = () => {
    if (window.openQuizModal) {
      window.openQuizModal(false, 'catalog');
    }
  };

  return (
    <>
      {/* Фиксированная панель навигации при скролле */}
      <div className={`fixed-mobile-nav ${scrolled ? 'visible' : ''}`}>
        <div className="mobile-nav-container">
          <div className="mobile-nav-logo">
            <img src={logoMtforce} alt="MTFORCE" />
          </div>
          <div className="mobile-nav-actions">
            <button
              className="download-catalog-button"
              onClick={openRegistrationForm}
              aria-label="Скачать каталог"
            >
              <Download size={20} />
              <span>Скачать каталог</span>
            </button>
            <button
              className="hamburger-button"
              onClick={toggleMobileMenu}
              aria-label={mobileMenuOpen ? "Закрыть меню" : "Открыть меню"}
            >
              <Menu size={24} />
            </button>
          </div>
        </div>
      </div>

      {/* Фиксированная кнопка гамбургера при скролле скрыта, так как у нас есть гамбургер в новой панели навигации */}

      {/* Мобильное меню */}
      <div className={`mobile-menu ${mobileMenuOpen ? 'mobile-menu-open' : ''}`}>
        <div className="mobile-menu-content">
          <div className="mobile-menu-header">
            <img src={logoMtforce} alt="MTFORCE" className="mobile-menu-logo" />
            <button
              className="mobile-menu-close"
              onClick={toggleMobileMenu}
              aria-label="Закрыть меню"
            >
              <X size={24} />
            </button>
          </div>
          <div className="mobile-menu-items">
            <a href="#" className="mobile-menu-item" onClick={toggleMobileMenu}>Главная</a>
            <a href="#assortment" className="mobile-menu-item" onClick={toggleMobileMenu}>Ассортимент</a>
            <a href="#partnership-steps" className="mobile-menu-item" onClick={toggleMobileMenu}>Как начать</a>
            <a href="#testimonials" className="mobile-menu-item" onClick={toggleMobileMenu}>Отзывы</a>
            <a href="#faq" className="mobile-menu-item" onClick={toggleMobileMenu}>Вопросы/Ответы</a>
          </div>
          <div className="mobile-menu-contact">
            <Button
              variant="accent"
              size="medium"
              fullWidth
              onClick={() => window.location.href = 'tel:+74951344778'}
            >
              <Phone size={18} /> ****** 134 47 78
            </Button>
            <div className="mobile-menu-hours">
              <Headphones size={18} className="operator-icon" />
              <div className="mobile-menu-hours-text">
                <div>Время работы</div>
                <div>Пн-Пт - С 9:00 до 18:00</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
