import React, { useEffect, useState } from 'react';
import './Header.scss';
import Button from './UI/Button';
import { Phone, Headphones } from 'lucide-react';
import logoMtforce from '../assets/icons/logo-mtforce.svg';
import yandexIcon from '../assets/icons/yandex.svg';
import gisIcon from '../assets/icons/2gis.svg';
import googleIcon from '../assets/icons/google.svg';

const Header = () => {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 100;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener('scroll', handleScroll);
    return () => {
      document.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  return (
    <header className="header">
      <div className="container header-container">
        <div className="logo-rating-group">
          <div className="logo">
            <a href="#" aria-label="MTFORCE">
              <img src={logoMtforce} alt="MTFORCE" className="logo-image" />
            </a>
          </div>

          <div className="trust-badges">
            <div className="trust-badge">
              <div className="rating-label">Рейтинг Яндекс</div>
              <div className="rating-value">
                <img src={yandexIcon} alt="Яндекс" className="rating-icon" />
                <span className="trust-badge-rating">5.0</span>
              </div>
            </div>
            <div className="trust-badge">
              <div className="rating-label">Рейтинг 2GIS</div>
              <div className="rating-value">
                <img src={gisIcon} alt="2GIS" className="rating-icon" />
                <span className="trust-badge-rating">5.0</span>
              </div>
            </div>
            <div className="trust-badge">
              <div className="rating-label">Рейтинг Google</div>
              <div className="rating-value">
                <img src={googleIcon} alt="Google" className="rating-icon" />
                <span className="trust-badge-rating">4.9</span>
              </div>
            </div>
          </div>
        </div>

        <div className="header-actions">
          <div className="contact-info">
            <div className="phone-container">
              <Button
                variant="accent"
                size="medium"
                onClick={() => window.location.href = 'tel:+74951344778'}
              >
                <Phone size={18} /> ****** 134 47 78
              </Button>
            </div>
            <div className="work-hours">
              <Headphones size={18} className="operator-icon" />
              <div className="work-hours-text">
                <div>Время работы</div>
                <div>Пн-Пт - С 9:00 до 18:00</div>
              </div>
            </div>
          </div>

          {/* Меню-гамбургер удалено */}
        </div>
      </div>

      {/* Убрана фиксированная кнопка при скролле */}
    </header>
  );
};

export default Header;
