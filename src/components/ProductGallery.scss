@use '../styles/variables' as *;

$gradient-cta: linear-gradient(135deg, $primary-color 0%, darken($primary-color, 15%) 100%) !default;
$slate-lighter: #f1f5f9 !default;
$white: #ffffff !default; // Добавим определение $white на всякий случай

.product-gallery {
  background-color: $slate-ultralight;
  position: relative;
  overflow: hidden;
   @media (min-width: $breakpoint-lg) {
         background-color:   $background-light;
      }



  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba($primary-color, 0.05) 0%, transparent 70%);
    z-index: 0;
    pointer-events: none;
  }
}

.section-header {
  position: relative;
  z-index: 1;
  margin-bottom: $spacing-2xl;
  text-align: center;

  
}

.section-subtitle {
  max-width: 700px;
  margin: $spacing-sm auto 0;
  font-size: 1.2rem;
  color: $text-color;
  line-height: 1.6;
}

.section-countdown {
  margin-top: $spacing-md;
  display: flex;
  justify-content: center;

  .countdown-inline {
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    gap: $spacing-xs;
    margin: 0 auto;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      align-items: center;
    }
  }
}

.product-grid {
  position: relative;
  z-index: 1;
  
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;

  @media (min-width: $breakpoint-md) {
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-lg;
  }

  @media (min-width: $breakpoint-lg) {
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-xl;
  }
}

.product-card-wrapper {
  /* Добавляем простую CSS-анимацию для плавного появления */
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
  width: 100%;

  &.cta-wrapper {
    @media (max-width: $breakpoint-lg) {
      grid-column: 1 / -1; // CTA карточка занимает всю ширину на мобильных
    }
  }

  &.last-before-cta {
    @media (max-width: $breakpoint-lg) {
      display: none; // Скрываем последнюю карточку перед CTA на мобильных
    }
  }
}

/* Анимация для плавного появления карточек */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card {
  background-color: $background-light;
  border-radius: $border-radius-lg;
  overflow: hidden;
  transition: box-shadow $transition-normal, transform $transition-normal;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: $shadow-sm;
  cursor: pointer; // Добавляем курсор pointer для указания кликабельности

  @media (min-width: $breakpoint-sm) {
    @media (hover: hover) and (pointer: fine) {
      &:hover {
        transform: translateY(-5px);
        box-shadow: $shadow-lg;

        .product-image .product-img,
        .product-image .lazy-video-container video {
          transform: scale(1.03);
        }
      }
    }
  }

  // --- Обновленные стили для CTA карточки ---
  &.cta-product-card {
    background-color: $white; // Чистый фон
    box-shadow: none; // Убираем стандартную тень
    border: 2px dashed $primary-light; // Заметная, но не агрессивная рамка
    padding: $spacing-lg;
    justify-content: center; // Выравниваем контент по центру вертикали
    align-items: center; // Выравниваем контент по центру горизонтали
    text-align: center; // Центрируем текст
    transition: border-style $transition-fast, background-color $transition-normal, box-shadow $transition-normal; // Добавляем переходы

    // Стили для заголовка внутри CTA
    .cta-card-title { // Предполагаем такой класс у заголовка в CtaCard.jsx
        font-size: $font-size-xl;
        font-weight: 600;
        color: $primary-dark;
        margin-bottom: $spacing-lg; // Отступ до кнопки
        line-height: 1.3;
    }

    // Стили для кнопки внутри CTA (предполагаем стандартный Button компонент)
    .button { // Используйте актуальный класс вашей кнопки
        // Можно добавить специфичные стили для кнопки в CTA, если нужно
    }
  
    @media (min-width: $breakpoint-sm) {
      @media (hover: hover) and (pointer: fine) {
        &:hover {
          transform: none; // Не поднимаем карточку
          border-style: solid; // Рамка становится сплошной
          border-color: $primary-color; // Цвет рамки темнее
          background-color: rgba($primary-color, 0.03); // Очень легкий фон при наведении
          box-shadow: $shadow-md; // Добавляем тень при наведении
        }
      }
    }
  }
  // --- Конец обновленных стилей для CTA ---
}


.product-image {
  width: 100%;
  aspect-ratio: 3 / 4;
  overflow: hidden;
  position: relative;
  display: block;
  background-color: $slate-lighter;

  &-placeholder {
    position: absolute;
    inset: 0;
    z-index: 1;
    overflow: hidden;
    background-color: $slate-lighter;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -150%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to right,
        transparent 0%,
        rgba(255, 255, 255, 0.5) 50%,
        transparent 100%
      );
      animation: shimmer-animation 1.8s infinite linear;
      transform: skewX(-25deg);
    }
  }

  @keyframes shimmer-animation {
    0% { left: -150%; }
    100% { left: 150%; }
  }

  .product-img,
  .lazy-video-container {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: relative;
    z-index: 2;
    background-color: $background-light;
    transition: transform $transition-slow ease-out;
  }

  &-error {
    position: absolute;
    inset: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba($slate, 0.05);
    color: $slate;
    padding: $spacing-sm;

    &-icon {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: $spacing-sm;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba($slate, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-text {
      font-size: $font-size-sm;
      text-align: center;
    }
    &.no-image {
       background-color: $slate-light;
       color: $slate-dark;
    }
  }
}

.product-info {
  padding: $spacing-md;
  margin-top: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
  min-height: 120px;
  background-color: $background-light;
  position: relative;
  z-index: 3;
}

.product-name {
  font-size: $font-size-base;
  font-weight: 600;
  margin-bottom: $spacing-sm;
  color: $dark-text;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: calc(1.4em * 2);
  word-break: break-word;

  @media (max-width: $breakpoint-lg) {
    font-size: $font-size-sm;
    min-height: calc(1.4em * 2);
  }
}

.product-price-wrapper {
  display: flex;
  margin-top: auto;

  @media (max-width: $breakpoint-lg) {
    flex-direction: column-reverse;
    gap: 2px;
  }

  @media (min-width: $breakpoint-lg) {
    align-items: baseline;
    gap: $spacing-xs;
  }
}

.product-price {
  font-weight: 700;
  color: $primary-color;
  font-size: $font-size-lg;
  line-height: 1;

  @media (max-width: $breakpoint-lg) {
    font-size: $font-size-md;
  }
}

.product-old-price {
  font-size: $font-size-sm;
  color: $slate;
  text-decoration: line-through;
  line-height: 1;

  @media (max-width: $breakpoint-lg) {
    font-size: $font-size-xs;
  }
}

.product-image-labels {
  position: absolute;
  bottom: $spacing-sm;
  left: $spacing-sm;
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
  z-index: 4;

  @media (max-width: $breakpoint-lg) {
    display: none; // Скрываем ярлыки на мобильных устройствах
  }
}

.product-label {
  display: inline-block;
  padding: 3px 8px;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 600;
  line-height: 1.2;
  color: white;
  white-space: nowrap;

  &-sale { background-color: #8362EE }
  &-sale strong { background: #01FF67;
    color: #000;
    padding: 0 0.25rem;
    border-radius: 0.30rem; }
  &-discount { background-color: rgba($red, 0.9); }
  &-new { background-color: rgba($green, 0.9); }
}

// Стили для кнопки "Смотреть все модели"
.view-all-container {
  display: flex;
  justify-content: center;
  margin-top:2rem;

}
  .button-tooltip{
      max-width: 400px;
    }
