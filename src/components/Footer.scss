@use '../styles/variables' as *;

.footer {
  background-color: darken($dark-text, 5%);
  color: $light-text;
  padding-top: 2rem;
  position: relative;
  overflow: hidden;
  @media (min-width: $breakpoint-lg) {
    padding-top: 4rem;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba($primary-color, 0.1) 0%, transparent 70%);
    z-index: 0;
  }
}

.stats-banner {
  padding: $spacing-md 0;
  position: relative;
  z-index: 1;
  border-top: 1px solid rgba($white, 0.08);
}

.stats-banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; // Плавный скролл на iOS
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE и Edge

  &::-webkit-scrollbar {
    display: none; // Chrome, Safari и Opera
  }

  @media (max-width: $breakpoint-lg) {
    justify-content: flex-start;
    padding-bottom: $spacing-xs; // Добавляем немного места для скролла
  }
}

.stat-item {
  display: flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  flex-shrink: 0; // Предотвращает сжатие элементов

  @media (max-width: $breakpoint-lg) {
    min-width: 180px; // Минимальная ширина для мобильных устройств
  }
}

.stat-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: $border-radius-sm;
  background-color: rgba($white, 0.05);
  margin-right: $spacing-sm;
  color: $white;
  flex-shrink: 0;
}

.stat-text {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-family: $font-family-main;
  font-size: $font-size-md;
  font-weight: 700;
  color: $white;
  line-height: 1.2;

    @media (max-width: $breakpoint-lg) {
   font-size: 1.5rem;
  }
}

.stat-label {
  font-size: $font-size-xs;
  color: rgba($light-text, 0.6);
  line-height: 1.2;
   @media (max-width: $breakpoint-lg) {
   font-size: 1rem;
  }
}

.stat-divider {
  width: 1px;
  height: 24px;
  background-color: rgba($white, 0.1);
  margin: 0 $spacing-md;
  flex-shrink: 0;
}

.footer-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
  position: relative;
  z-index: 1;
}

.footer-column {
  margin-bottom: $spacing-lg;
}

.footer-top-row {
  display: flex;
  flex-direction: column;
  width: 100%;

  @media (min-width: $breakpoint-lg) {
    flex-direction: row;
    gap: $spacing-2xl;
    align-items: flex-start;
  }
}

.footer-brand {
  width: 100%;

  @media (min-width: $breakpoint-md) {
    flex: 1;
    max-width: 500px;
  }
}

.footer-contact-column {
  width: 100%;

  @media (min-width: $breakpoint-md) {
    flex: 1;
  }
}



.footer-logo {
  margin-bottom: $spacing-md;
  display: flex;
  align-items: center;

  @media (max-width: $breakpoint-lg) {
    justify-content: center;
  }

  .logo-image {
    max-width: 120px;
    height: auto;
  }
}

.footer-description {
  font-size: $font-size-md;
  color: rgba($light-text, 0.85);
  max-width: 400px;
  margin-bottom: $spacing-md;
  @media (max-width: $breakpoint-lg) {
    text-align: center;
  }
}

.footer-social-inline {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
  @media (max-width: $breakpoint-lg) {
    justify-content: center;
  }
}

.footer-social-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--color);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 0;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border-color: transparent;

    &::before {
      opacity: 1;
    }
  }
}

.footer-social-icon {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
  opacity: 0.8;
  transition: all 0.3s ease; // Используем такую же анимацию, как у кнопки "Перейти на mtforce.ru"
  position: relative;
  z-index: 1;
  will-change: transform, opacity; // Оптимизация производительности

  .footer-social-circle:hover & {
    opacity: 1;
    transform: scale(1.3);
  }
}

.footer-address-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  margin-bottom: $spacing-lg;
  transition: all 0.3s ease; // Используем такую же анимацию, как у кнопки "Перейти на mtforce.ru"

  &:hover {
    transform: translateY(-2px);

    .footer-address-text {
      color: $light-text;
    }
  }
  @media (max-width: $breakpoint-lg) {
    justify-content: center;
    margin: 2rem 0 1.5rem  0;
    flex-direction: column;
  }
}

.footer-address-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 87, 34, 0.1);
  margin-right: $spacing-md;
  color: #FF5722;
  flex-shrink: 0;
}

.footer-address-text {
  color: rgba($light-text, 0.9);
  font-size: $font-size-md;
  transition: color 0.3s ease; // Используем такую же анимацию, как у кнопки "Перейти на mtforce.ru"
}

.footer-title {
  font-size: $font-size-lg;
  font-weight: 700;
  margin-bottom: $spacing-md;
  color: $light-text;
  position: relative;

  &::after {
    content: '';
    display: block;

 margin-top: 0.25rem;

    width: 40px;
    height: 3px;
    background-color: $primary-color;
    border-radius: $border-radius-full;

    @media (max-width: $breakpoint-lg) {
      margin-left: auto;
      margin-right: auto;
    }

  }
  @media (max-width: $breakpoint-lg) {
    text-align: center;
  }

}

.footer-contact-section {
  margin-top: $spacing-xl;
}

.footer-subtitle {
  font-size: $font-size-md;
  font-weight: 600;
  margin-bottom: $spacing-md;
  color: $light-text;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -$spacing-xs;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: $primary-color;
    border-radius: $border-radius-full;
  }
}

.footer-contact-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
  }
}

.footer-contact-card {
  display: flex;
  align-items: center;
  padding: $spacing-sm $spacing-md;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: $border-radius-md;
  text-decoration: none;
  transition: all 0.3s ease; // Явно задаем плавную анимацию
  position: relative;
  overflow: hidden;

    @media (max-width: $breakpoint-lg) {
    font-size: 1.2;
     padding: $spacing-md;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: $primary-color;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  &.footer-contact-phone {
    &::before {
      background-color: #FF5722; // Оранжевый цвет
    }

    &:hover {
      background-color: rgba(255, 87, 34, 0.05);
    }
  }

  &.footer-contact-email {
    &::before {
      background-color: #2196F3;
    }

    &:hover {
      background-color: rgba(33, 150, 243, 0.05);
    }
  }

  &.footer-contact-whatsapp {
    &::before {
      background-color: #25D366;
    }

    &:hover {
      background-color: rgba(37, 211, 102, 0.05);
    }
  }

  &.footer-contact-viber {
    &::before {
      background-color: #7360f2;
    }

    &:hover {
      background-color: rgba(115, 96, 242, 0.05);
    }
  }

  &.footer-contact-website {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2); // Серая обводка
    transition: all 0.3s ease;

    &::before {
      display: none; // Убираем цветную полоску слева
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1); // Серый фон при наведении
      border-color: rgba(255, 255, 255, 0.4);
    }
  }

  &.footer-contact-address::before {
    background-color: #FF5722;
  }
}

.footer-contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  margin-right: $spacing-md;
  color: $light-text;
  flex-shrink: 0;
}

.footer-contact-text {
  color: rgba($light-text, 0.95);
  font-size: $font-size-md;
  flex-grow: 1;
}

.footer-external-icon {
  color: rgba($light-text, 0.5);
  margin-left: $spacing-sm;
  flex-shrink: 0;
}



.footer a,
.footer-link {
  color: rgba($light-text, 0.7);
  text-decoration: none !important;
  transition: color 0.3s ease; // Используем такую же анимацию, как у кнопки "Перейти на mtforce.ru"

  &:hover {
    color: $light-text;
    text-decoration: none !important;
  }
}

.footer-bottom {
  border-top: 1px solid rgba($light-text, 0.1);
  padding: $spacing-lg 0;

  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  position: relative;
  z-index: 1;

  @media (min-width: $breakpoint-md) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.footer-company-info,
.footer-copyright-text {
  font-size: $font-size-base;
  color: rgba($light-text, 0.7);
  margin: 0;
}
