import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Check, CloudDownload, FileCode, Sheet } from 'lucide-react';
import Button from './UI/Button';
import QuizModal from './UI/QuizModal';
import qParserIcon from '../assets/Q-parser.svg';
import './PartnershipSteps.scss';

const PartnershipSteps = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.01,
    rootMargin: '0px 0px -10% 0px',
  });

  const isMobile = window.innerWidth < 768;

  // Массив с типами экспорта для отображения рядом с кнопкой
  const exportTypes = [
    { iconType: 'custom', iconSrc: qParserIcon, name: 'Q-parser', colorClass: 'icon-color-api' },
    { iconType: 'lucide', Icon: CloudDownload, name: '<PERSON>Parser', colorClass: 'icon-color-cloud' },
    { iconType: 'lucide', Icon: FileCode, name: 'YML', colorClass: 'icon-color-yml' },
    { iconType: 'lucide', Icon: Sheet, name: 'CSV', colorClass: 'icon-color-csv' },
  ];

  const steps = [
    {
      title: 'Изучите ассортимент и цены',
      description: (
        <>
          <p>Нажмите кнопку «Выгрузить каталог» прямо сейчас и заполните форму, чтобы получить доступ к полному каталогу одежды по честным оптовым ценам без посредников.</p>
        </>
      ),
      badges: [
        { text: '1300+ моделей', type: 'primary' },
        { text: 'Каталог для СП', type: 'secondary' }
      ]
    },
    {
      title: 'Открытие СП / Сбор заказов',
      description: (
        <>
          <p>Используйте готовые рекламные материалы и объявите о старте СП в вашей группе или на сайте.</p>
          <p>Вы легко соберете минимальный заказ и получите хороший оргсбор, ведь в России бренд MTFORCE уже 10 лет знают и ценят за хорошее качество и доступные цены.</p>
        </>
      ),
      badges: [
        { text: 'Легко продавать', type: 'primary' },
        { text: 'Стабильный оргсбор', type: 'secondary' }
      ]
    },
    {
      title: 'Бронирование товара без оплаты',
      description: (
        <>
          <p>По мере получения заказов от участников, резервируйте нужные модели до 7 дней без оплаты.</p>
          <p>Это гарантирует, что ваши клиенты точно получат свои заказы, а вы не будете беспокоиться о наличии.</p>
        </>
      ),
      badges: [
        { text: 'Бронь без предоплаты', type: 'primary' },
        { text: 'Гарантия наличия', type: 'secondary' }
      ]
    },
    {
      title: 'Оформление и оплата',
      description: (
        <>
          <p>Когда ваш заказ будет готов, сформируйте счет на оплату в личном кабинете. Стандартная минимальная сумма заказа составляет 24 900 ₽.</p>

          <p>Не набрали нужную сумму? Не переживайте! Свяжитесь с нами — мы готовы обсудить индивидуальные условия, снизить порог и гарантированно отгрузить ваш заказ.</p>

          <p>Оплатите выставленный счет без комиссии любым удобным для вас способом:</p>
          <ul className="process-step-list">
            <li>Перевод на расчетный счет (р/с)</li>
            <li>Оплата картой онлайн или через СБП</li>
            <li>Перевод на карту Сбербанка</li>
            <li>Наличными</li>
            <li>В рассрочку или кредит</li>
          </ul>
        </>
      ),
      badges: [
        { text: 'Сейчас мин. заказ всего 9 990 ₽', type: 'primary' },
        { text: 'Оплата 0% комиссии', type: 'accent' }
      ]
    },
    {
      title: 'Получение и выдача заказов',
      description: (
        <>
          <p>После получения от вас оплаты мы собираем, бережно упаковуем и отгрузим заказ до выбранной вами ТК в течение 72 часов.</p>
          <p>Вы получаете товар со всеми необходимыми документами и маркировкой «Честный ЗНАК».</p>
        </>
      ),
      badges: [
        { text: 'Средняя скорость отгрузки 1 день', type: 'primary' },
        { text: 'Доставка до ТК 0 ₽ (в пределах МКАД)', type: 'secondary' },
        { text: 'Честный ЗНАК', type: 'accent' }
      ]
    },
    {
      title: 'Возврат и обмен',
      description: (
        <>
          <ul className="process-step-list">
            <li>Возврат возможен в течение 14 дней с момента получения заказа.</li>
            <li>Товары должны быть в оригинальной упаковке, с бирками и без следов ношения.</li>
            <li>Обмен — за наш счёт, если получен товар с браком или не тот артикул.</li>
            <li>Обратная доставка при возврате по инициативе покупателя — за счёт клиента.</li>
          </ul>
        </>
      ),
      badges: [
        { text: 'Лёгкий возврат или обмен', type: 'secondary' }
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: isMobile ? 0.1 : 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: isMobile ? 0.2 : 0.4,
      },
    },
  };

  return (
    <section className="partnership-steps section" id="partnership-steps">
      <div className="container">
        <motion.div
          className="section-header text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="section-title text-center">
            6 простых шагов к выгодному партнерству с MTFORCE
          </h2>
        </motion.div>

        <motion.div
          className="process-container"
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
        >
          {steps.map((step, index) => (
            <motion.div
              className="process-step"
              key={index}
              variants={itemVariants}
            >
              <h3 className="process-step-title">{step.title}</h3>
              <div className="process-step-description">
                {typeof step.description === 'string' ? <p>{step.description}</p> : step.description}
              </div>

              {index === 0 && (
                <div className="process-step-cta">
                  <div className="process-step-cta-container">
                    <Button
                      variant="primary"
                      size="medium"
                      onClick={() => window.openQuizModal ? window.openQuizModal(false, 'partnership_steps') : null}
                      className="process-step-cta-button"
                      
                    >
                      Выгрузить каталог
                    </Button>

                    <div className="process-step-export-types">
                      {exportTypes.map((item) => (
                        <div key={item.name} className="process-step-export-type">
                          {item.iconType === 'custom' ? (
                            <img
                              src={item.iconSrc}
                              alt={item.name}
                              className={`process-step-export-icon ${item.colorClass}`}
                              width="32"
                              height="32"
                              style={{ maxWidth: '32px', maxHeight: '32px' }}
                            />
                          ) : (
                            <item.Icon
                              className={`process-step-export-icon ${item.colorClass}`}
                              size={32}
                              strokeWidth={1.5}
                            />
                          )}
                          <span className="process-step-export-name">{item.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="process-step-badges">
                {step.badges.map((badge, badgeIndex) => (
                  <span
                    key={badgeIndex}
                    className={`process-step-badge process-step-badge-${badge.type}`}
                  >
                    <span className="process-step-badge-icon"><Check size={14} strokeWidth={2.5} /></span>
                    {badge.text}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>


    </section>
  );
};

export default PartnershipSteps;
