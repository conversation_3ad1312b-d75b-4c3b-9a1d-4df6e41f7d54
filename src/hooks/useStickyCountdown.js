import { useState, useEffect } from 'react';

/**
 * Хук для управления прилипающим таймером
 * Показывает таймер после прокрутки определенного расстояния
 */
export const useStickyCountdown = (triggerOffset = 200) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsVisible(scrollTop > triggerOffset);
    };

    // Добавляем обработчик скролла
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Проверяем начальное состояние
    handleScroll();

    // Очищаем обработчик при размонтировании
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [triggerOffset]);

  return isVisible;
};
