openapi: 3.0.0
info:
  title: MTFORCE API Gateway
  version: 1.0.0
  description: API Gateway для интеграции с AmoCRM

paths:
  /send-lead:
    post:
      summary: Отправка лида в AmoCRM
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ef5i81ro6cdhm2m813
        tag: $latest
        service_account_id: ajerg9mrvl4ilt0iusdj
      x-yc-apigateway-throttling:
        enabled: true
        rate: 15
        period: 60  # 15 запросов в минуту
      responses:
        '200':
          description: Успешная отправка лида
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '400':
          description: Ошибка в запросе
        '500':
          description: Внутренняя ошибка сервера
    options:
      summary: CORS preflight request
      x-yc-apigateway-integration:
        type: cloud_functions
        function_id: d4ef5i81ro6cdhm2m813
        tag: $latest
        service_account_id: ajerg9mrvl4ilt0iusdj
      responses:
        '200':
          description: CORS headers
