# MTFORCE - Документация проекта

## Обзор проекта

MTFORCE - это лендинг для оптового поставщика одежды, ориентированный на организаторов совместных покупок в России. Проект разработан с использованием современных технологий и оптимизирован для мобильных устройств.

## Специфика проекта

### Ключевые особенности

1. **Мобильный приоритет**
   - Проект разрабатывается по принципу "mobile-first"
   - На мобильных устройствах не отображаются гамбургер-меню, номер телефона и кнопка "Скачать каталог"
   - Рейтинги более компактные на мобильных устройствах
   - Галерея товаров отображается в один ряд с горизонтальной прокруткой

2. **Прелоадер и оптимизация загрузки**
   - Прелоадер реализован на чистом CSS и ванильном JavaScript
   - Используется прогрессивная загрузка изображений
   - Шрифты загружаются локально в формате WOFF2
   - Прелоадер скрывается через `window.hidePreloader()` после загрузки React

3. **Интеграция с AmoCRM**
   - Используется OAuth-авторизация
   - Интеграция реализована через Яндекс.Функции (ранее Cloudflare Worker)
   - URL: mtforcedirect.amocrm.ru, клиент #32366870
   - ID полей: Имя (70640), Телефон (706407), Email (706409)

4. **Аналитика**
   - Интеграция с Яндекс.Метрикой (ID счетчика: 101165245)
   - Настроен вебвизор
   - Цели: "Лид" (ID: 404032590, событие: 'lid') и "Лид+Опрос" (ID: 404033715, событие: 'lidQuiz')

5. **Дизайн и стилизация**
   - Современный дизайн 2025 года с более светлой темой на основе цвета #fafafb
   - Красные, более высокие кнопки с современными эффектами при наведении
   - Анимации реализованы с помощью Framer Motion
   - Заголовки разделов имеют размер 1.75rem

6. **Квиз и формы**
   - Квиз отображается в модальном окне
   - Фиксированная кнопка "Get Gift" появляется после прокрутки до раздела с преимуществами
   - Все кнопки открывают финальную форму квиза с упоминанием бонусного счета на 10,000₽

### Важные нюансы разработки

1. **Кодирование**
   - Не добавлять комментарии в код
   - Использовать современные функции JavaScript (ES6+)
   - Использовать функциональные компоненты и хуки React

2. **Оптимизация**
   - Целевой показатель Lighthouse: 100 баллов
   - Оптимизировать работу главного потока, структуру DOM и LCP
   - Изображения оптимизированы до размера 600x750px, сжаты и конвертированы в WebP
   - Видео оптимизированы и загружаются с использованием стриминга

3. **Деплой**
   - Используется Яндекс.Облако для хостинга (ранее Cloudflare Pages)
   - Статический сайт доступен на субдомене sp.mtforce.club
   - Основной домен mtforce.club будет использоваться для другого проекта

## Структура проекта

```
lend2/
├── public/                  # Статические файлы
│   ├── fonts/               # Шрифты
│   ├── icons/               # Иконки
│   ├── images/              # Изображения
│   └── index.html           # Шаблон HTML
├── scripts/                 # Скрипты для сборки
│   ├── copy-fonts.js        # Копирование шрифтов
│   ├── optimize-images.js   # Оптимизация изображений
│   └── optimize-videos.js   # Оптимизация видео
├── src/                     # Исходный код
│   ├── assets/              # Ресурсы (изображения, иконки)
│   ├── components/          # React компоненты
│   │   ├── UI/              # UI компоненты (кнопки, модалки и т.д.)
│   │   └── ...              # Секции лендинга
│   ├── data/                # Данные (JSON)
│   ├── hooks/               # React хуки
│   ├── styles/              # Глобальные стили
│   ├── utils/               # Утилиты
│   ├── App.jsx              # Главный компонент
│   └── main.jsx             # Точка входа
├── mtforce-amo-integration/ # Интеграция с AmoCRM (Яндекс.Облако Worker)
├── примеры/                # Примеры HTML и интеграций
├── bucket-policy.json       # Политика доступа к бакету Яндекс.Облака
├── cors-config.json         # Настройки CORS для бакета
├── website-config.json      # Конфигурация статического сайта
├── index.html               # Основной HTML файл
├── vite.config.js           # Конфигурация Vite
└── package.json             # Зависимости и скрипты
```

## Технологический стек

- **Frontend**: React 19, Vite 6
- **Стилизация**: SCSS
- **Анимации**: Framer Motion
- **Иконки**: Lucide React
- **Оптимизация**: Собственные скрипты для оптимизации изображений и видео
- **Интеграции**: AmoCRM через Яндекс.Функции
- **Деплой**: Яндекс.Облако (бакет с настройками статического сайта)
- **Аналитика**: Яндекс.Метрика с вебвизором

## Процесс сборки

Проект использует Vite для сборки и оптимизации. Процесс сборки включает:

1. **Оптимизация видео**: Конвертация видео в оптимизированные форматы
2. **Оптимизация изображений**: Сжатие и конвертация изображений в WebP
3. **Сборка React**: Компиляция и минификация React компонентов
4. **Сжатие**: Генерация gzip и brotli версий файлов

Команда для сборки:
```bash
npm run build
```

## Особенности проекта

### Прелоадер

Проект использует кастомный прелоадер, который:
- Отображается до инициализации React
- Загружает критические ресурсы (шрифты, основные изображения)
- Автоматически скрывается после загрузки React

### Оптимизация для мобильных устройств

- Адаптивный дизайн
- Оптимизированные изображения
- Разные компоненты для мобильных и десктопных устройств

### Интеграция с AmoCRM

Интеграция с AmoCRM реализована через Яндекс.Облако Worker, который:
- Обрабатывает отправку форм
- Управляет OAuth-авторизацией
- Создает контакты и сделки в AmoCRM
- Отправляет данные в Яндекс.Метрику

## Важные замечания

1. **Не добавляйте комментарии в код**. Проект должен быть чистым и понятным без лишних комментариев.
2. **Используйте Git** для управления версиями проекта.
3. **Оптимизируйте изображения** перед добавлением в проект.
4. **Следуйте структуре проекта** при добавлении новых компонентов.

## Деплой

Проект деплоится на Яндекс.Облако:

### Настройка бакета

1. Бакет `sp.mtforce.club` должен быть настроен с публичным доступом
2. Настройки CORS должны быть применены из файла `cors-config.json`
3. Настройки статического сайта должны быть применены из файла `website-config.json`

### Процесс деплоя

1. Сборка проекта: `npm run build`
2. Загрузка файлов в бакет: `aws --endpoint-url=https://storage.yandexcloud.net s3 sync ./dist/ s3://sp.mtforce.club/ --delete`

### Деплой воркера для AmoCRM

Воркер для интеграции с AmoCRM должен быть развернут на Яндекс.Функциях и настроен для обработки запросов с домена `sp.mtforce.club`.

### Параметры Яндекс Облака

- **Cloud ID**: b1g1jjuglcqn3hua4i47 (cloud-mtforce-direct)
- **Folder ID**: b1goaqo33m83k91csjf5 (default)
- **Function ID**: d4ec8lk7lq5t73upoull (mtforce-amo-integration)
- **Function URL**: https://functions.yandexcloud.net/d4ec8lk7lq5t73upoull

### Параметры AmoCRM

- **Домен**: mtforcedirect.amocrm.ru
- **Client ID**: dd804a74-e771-441e-a8dc-6f0df48fe58a
- **Redirect URI**: https://sp.mtforce.club/auth-callback
- **ID полей**: Имя (70640), Телефон (706407), Email (706409)

## Контакты

Разработчик: MTFORCE Developer (<EMAIL>)
