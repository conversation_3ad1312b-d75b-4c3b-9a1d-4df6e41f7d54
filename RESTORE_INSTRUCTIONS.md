# Инструкция по восстановлению проекта MTFORCE Landing

Этот документ содержит инструкции по восстановлению проекта MTFORCE Landing до состояния полной резервной копии, созданной 19 мая 2025 года.

## Информация о резервной копии

- **Тег**: `v1.0.0-full-backup`
- **Хеш коммита**: `be55fabc709f3fe3b34be815c7f67b542a2e72c6`
- **Описание**: Полная резервная копия проекта MTFORCE Landing, включающая все файлы, конфигурации и ресурсы.

## Способы восстановления

### Способ 1: Восстановление по тегу (рекомендуется)

```bash
# Перейти в директорию проекта
cd path/to/lend2

# Убедиться, что у вас нет несохраненных изменений
git status

# Восстановить проект до состояния тега
git checkout v1.0.0-full-backup

# Если вы хотите продолжить работу с этой версией, создайте новую ветку
git checkout -b new-branch-name
```

### Способ 2: Восстановление по хешу коммита

```bash
# Перейти в директорию проекта
cd path/to/lend2

# Убедиться, что у вас нет несохраненных изменений
git status

# Восстановить проект до состояния конкретного коммита
git checkout be55fabc709f3fe3b34be815c7f67b542a2e72c6

# Если вы хотите продолжить работу с этой версией, создайте новую ветку
git checkout -b new-branch-name
```

### Способ 3: Восстановление отдельных файлов

Если вам нужно восстановить только определенные файлы, не затрагивая остальные:

```bash
# Восстановить конкретный файл
git checkout v1.0.0-full-backup -- path/to/file

# Пример: восстановить файл index.html
git checkout v1.0.0-full-backup -- index.html

# Пример: восстановить все файлы в директории src/components
git checkout v1.0.0-full-backup -- src/components/
```

### Способ 4: Восстановление удаленных файлов

Если вам нужно восстановить файлы, которые были удалены после создания резервной копии:

```bash
# Восстановить удаленный файл
git checkout v1.0.0-full-backup -- path/to/deleted/file

# Пример: восстановить удаленный файл src/utils/markdownParser.js
git checkout v1.0.0-full-backup -- src/utils/markdownParser.js
```

## После восстановления

После восстановления проекта рекомендуется выполнить следующие действия:

1. Установить зависимости:
   ```bash
   npm install
   ```

2. Убедиться, что проект запускается корректно:
   ```bash
   npm run dev
   ```

3. Проверить сборку проекта:
   ```bash
   npm run build
   ```

## Важные замечания

- Восстановление проекта до предыдущего состояния может привести к потере несохраненных изменений. Убедитесь, что вы сохранили все важные изменения перед восстановлением.
- Если вы работаете в команде, сообщите другим разработчикам о восстановлении проекта до предыдущего состояния.
- Если у вас возникли проблемы с восстановлением, обратитесь к документации Git или к разработчику, создавшему резервную копию.

## Контакты для поддержки

Если у вас возникли вопросы или проблемы с восстановлением проекта, обратитесь к разработчику:

- **Email**: <EMAIL>
