import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import ffmpegStatic from 'ffmpeg-static';
import { fileURLToPath } from 'url';

// Получаем текущую директорию в ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Путь к FFmpeg
const ffmpegPath = ffmpegStatic;

// Пути к директориям
const sourceDir = path.resolve(__dirname, '../src/assets/images/testimonials');
const targetDir = path.resolve(__dirname, '../public/images/testimonials');

// Создаем директорию, если она не существует
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Получаем список всех файлов в директории
const files = fs.readdirSync(sourceDir);

// Фильтруем только видео файлы
const videoFiles = files.filter(file => {
  const ext = path.extname(file).toLowerCase();
  return ext === '.mp4' || ext === '.webm' || ext === '.mov';
});

console.log(`Найдено ${videoFiles.length} видео файлов отзывов для оптимизации`);

// Оптимизируем каждый видео файл
videoFiles.forEach(file => {
  const sourcePath = path.join(sourceDir, file);
  const targetPath = path.join(targetDir, file);

  console.log(`Оптимизация видео отзыва ${file}...`);

  try {
    // Создаем HLS (HTTP Live Streaming) версию видео для отзывов
    const hlsDir = path.join(targetDir, path.basename(file, path.extname(file)));
    if (!fs.existsSync(hlsDir)) {
      fs.mkdirSync(hlsDir, { recursive: true });
    }

    const hlsPath = path.join(hlsDir, 'playlist.m3u8');

    // Создаем оптимизированную MP4 версию для быстрой загрузки
    const optimizedMp4Path = path.join(targetDir, file);
    const optimizeMp4Command = `"${ffmpegPath}" -i "${sourcePath}" \\
      -c:v libx264 -crf 23 -preset fast \\
      -profile:v baseline -level 3.0 \\
      -movflags +faststart \\
      -vf "scale=640:-2" \\
      -c:a aac -b:a 128k \\
      -y "${optimizedMp4Path}"`;

    execSync(optimizeMp4Command, { stdio: 'inherit' });

    // Создаем стандартный HLS стрим по рекомендациям Apple
    const command = `"${ffmpegPath}" -i "${sourcePath}" \\
      -preset fast -crf 23 \\
      -profile:v baseline -level 3.0 \\
      -start_number 0 -hls_time 6 -hls_list_size 0 \\
      -hls_segment_type mpegts \\
      -hls_playlist_type vod \\
      -hls_flags independent_segments \\
      -master_pl_name master.m3u8 \\
      -var_stream_map "v:0,name:240p v:1,name:480p v:2,name:720p" \\
      -b:v:0 400k -s:v:0 426x240 -maxrate:v:0 500k -bufsize:v:0 600k \\
      -b:v:1 1200k -s:v:1 854x480 -maxrate:v:1 1400k -bufsize:v:1 1800k \\
      -b:v:2 2500k -s:v:2 1280x720 -maxrate:v:2 2800k -bufsize:v:2 3000k \\
      -g 48 -keyint_min 48 \\
      -sc_threshold 0 \\
      -hls_segment_filename "${hlsDir}/%v_%03d.ts" \\
      -f hls "${hlsPath}"`;

    execSync(command, { stdio: 'inherit' });

    console.log(`✅ Видео отзыв ${file} успешно оптимизировано`);
  } catch (error) {
    console.error(`❌ Ошибка при оптимизации ${file}:`, error);
  }
});

console.log('Оптимизация видео отзывов завершена!');
