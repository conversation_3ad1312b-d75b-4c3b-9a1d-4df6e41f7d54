import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Получаем текущую директорию в ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Создаем директорию для шрифтов в dist, если она не существует
const distFontsDir = path.join(process.cwd(), 'dist', 'fonts');
if (!fs.existsSync(distFontsDir)) {
  fs.mkdirSync(distFontsDir, { recursive: true });
}

// Копируем все файлы из public/fonts в dist/fonts
const publicFontsDir = path.join(process.cwd(), 'public', 'fonts');
const files = fs.readdirSync(publicFontsDir);

files.forEach(file => {
  const sourcePath = path.join(publicFontsDir, file);
  const destPath = path.join(distFontsDir, file);

  // Проверяем, что это файл, а не директория
  if (fs.statSync(sourcePath).isFile()) {
    fs.copyFileSync(sourcePath, destPath);
    console.log(`✅ Скопирован файл шрифта: ${file}`);
  }
});

console.log('Копирование шрифтов завершено!');
