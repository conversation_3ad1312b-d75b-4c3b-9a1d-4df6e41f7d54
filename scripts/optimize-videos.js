import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import ffmpegStatic from 'ffmpeg-static';
import { fileURLToPath } from 'url';

// Получаем текущую директорию в ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Путь к FFmpeg
const ffmpegPath = ffmpegStatic;

// Пути к директориям
const sourceDir = path.resolve(__dirname, '../src/assets/images/product');
const targetDir = path.resolve(__dirname, '../public/images/product');

// Создаем директорию, если она не существует
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Получаем список всех файлов в директории
const files = fs.readdirSync(sourceDir);

// Фильтруем только видео файлы
const videoFiles = files.filter(file => {
  const ext = path.extname(file).toLowerCase();
  return ext === '.mp4' || ext === '.webm' || ext === '.mov';
});

console.log(`Найдено ${videoFiles.length} видео файлов для оптимизации`);

// Оптимизируем каждый видео файл
videoFiles.forEach(file => {
  const sourcePath = path.join(sourceDir, file);
  const targetPath = path.join(targetDir, file);

  console.log(`Оптимизация ${file}...`);

  try {
    // Для видео в каталоге товаров создаем максимально оптимизированную MP4 версию без звука
    // Используем подход как в крупных интернет-магазинах для миникарточек товаров
    const optimizedCommand = `"${ffmpegPath}" -i "${sourcePath}" \\
      -an \\
      -vcodec libx264 -crf 28 -preset veryslow \\
      -tune fastdecode \\
      -movflags +faststart -pix_fmt yuv420p \\
      -vf "scale=480:-2, fps=24" \\
      -profile:v baseline -level 3.0 \\
      -maxrate 800k -bufsize 1000k \\
      -g 48 -keyint_min 48 \\
      -sc_threshold 0 \\
      -x264opts "no-cabac:8x8dct=0:ref=2:bframes=0:weightp=0:me=dia:subme=0:trellis=0" \\
      "${targetPath}"`;

    execSync(optimizedCommand, { stdio: 'inherit' });

    console.log(`✅ Видео ${file} успешно оптимизировано`);
  } catch (error) {
    console.error(`❌ Ошибка при оптимизации ${file}:`, error);
  }
});

console.log('Оптимизация видео завершена!');
