const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Пути к директориям
const VIDEOS_DIR = path.join(__dirname, '../public/videos/testimonials');
const IMAGES_DIR = path.join(__dirname, '../public/images/testimonials');

// Создаем директории, если они не существуют
if (!fs.existsSync(VIDEOS_DIR)) {
  fs.mkdirSync(VIDEOS_DIR, { recursive: true });
}

if (!fs.existsSync(IMAGES_DIR)) {
  fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

// Функция для конвертации видео в HLS
function convertVideoToHLS(videoFile) {
  const videoName = path.basename(videoFile, path.extname(videoFile));
  const outputDir = path.join(VIDEOS_DIR, videoName);
  
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const inputPath = path.join(VIDEOS_DIR, videoFile);
  const outputPath = path.join(outputDir, 'playlist.m3u8');
  
  console.log(`Конвертация ${videoFile} в HLS...`);
  
  try {
    execSync(`ffmpeg -i "${inputPath}" -profile:v baseline -level 3.0 -start_number 0 -hls_time 10 -hls_list_size 0 -f hls "${outputPath}"`, { stdio: 'inherit' });
    console.log(`Видео ${videoFile} успешно конвертировано в HLS!`);
  } catch (error) {
    console.error(`Ошибка при конвертации видео ${videoFile}:`, error.message);
  }
}

// Функция для оптимизации изображений
function optimizeImage(imageFile) {
  const imagePath = path.join(IMAGES_DIR, imageFile);
  const imageName = path.basename(imageFile, path.extname(imageFile));
  const outputPath = path.join(IMAGES_DIR, `${imageName}.webp`);
  
  console.log(`Оптимизация ${imageFile}...`);
  
  try {
    // Конвертация в WebP с оптимальным качеством
    execSync(`convert "${imagePath}" -resize 640x360 -quality 80 "${outputPath}"`, { stdio: 'inherit' });
    console.log(`Изображение ${imageFile} успешно оптимизировано!`);
  } catch (error) {
    console.error(`Ошибка при оптимизации изображения ${imageFile}:`, error.message);
  }
}

// Обработка всех видео в директории
function processVideos() {
  try {
    const files = fs.readdirSync(VIDEOS_DIR);
    const videoFiles = files.filter(file => 
      file.endsWith('.mp4') && !fs.statSync(path.join(VIDEOS_DIR, file)).isDirectory()
    );
    
    if (videoFiles.length === 0) {
      console.log('Видео-файлы не найдены в директории.');
      return;
    }
    
    console.log(`Найдено ${videoFiles.length} видео-файлов для обработки.`);
    
    for (const videoFile of videoFiles) {
      convertVideoToHLS(videoFile);
    }
  } catch (error) {
    console.error('Ошибка при обработке видео:', error.message);
  }
}

// Обработка всех изображений в директории
function processImages() {
  try {
    const files = fs.readdirSync(IMAGES_DIR);
    const imageFiles = files.filter(file => 
      (file.endsWith('.jpg') || file.endsWith('.jpeg') || file.endsWith('.png')) && 
      !fs.statSync(path.join(IMAGES_DIR, file)).isDirectory()
    );
    
    if (imageFiles.length === 0) {
      console.log('Изображения не найдены в директории.');
      return;
    }
    
    console.log(`Найдено ${imageFiles.length} изображений для обработки.`);
    
    for (const imageFile of imageFiles) {
      optimizeImage(imageFile);
    }
  } catch (error) {
    console.error('Ошибка при обработке изображений:', error.message);
  }
}

// Запуск обработки
console.log('Начало обработки медиа-файлов...');
processVideos();
processImages();
console.log('Обработка медиа-файлов завершена!');
