import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';
import imagemin from 'imagemin';
import imageminWebp from 'imagemin-webp';
import imageminMozjpeg from 'imagemin-mozjpeg';
import imageminPngquant from 'imagemin-pngquant';

// Получаем текущую директорию в ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Пути к директориям
const sourceDir = path.resolve(__dirname, '../src/assets');
const targetDir = path.resolve(__dirname, '../public');

// Максимальные размеры изображений
const MAX_WIDTH = 900;
const MAX_HEIGHT = 1140;

// Качество сжатия
const QUALITY = 85;

// Создаем директорию, если она не существует
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Функция для рекурсивного обхода директорий
async function processDirectory(sourceDir, targetDir) {
  // Создаем целевую директорию, если она не существует
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }

  // Получаем список всех файлов и директорий
  const entries = fs.readdirSync(sourceDir, { withFileTypes: true });

  // Обрабатываем каждый элемент
  for (const entry of entries) {
    const sourcePath = path.join(sourceDir, entry.name);
    const targetPath = path.join(targetDir, entry.name);

    if (entry.isDirectory()) {
      // Рекурсивно обрабатываем поддиректории
      await processDirectory(sourcePath, targetPath);
    } else {
      // Обрабатываем файлы
      const ext = path.extname(entry.name).toLowerCase();
      if (['.jpg', '.jpeg', '.png', '.webp'].includes(ext)) {
        await optimizeImage(sourcePath, targetPath, ext);
      } else {
        // Копируем остальные файлы без изменений
        fs.copyFileSync(sourcePath, targetPath);
      }
    }
  }
}

// Функция для оптимизации изображения
async function optimizeImage(sourcePath, targetPath, ext) {
  try {
    console.log(`Оптимизация ${sourcePath}...`);

    // Получаем информацию об изображении
    const metadata = await sharp(sourcePath).metadata();

    // Определяем новые размеры с сохранением пропорций
    let width = metadata.width;
    let height = metadata.height;

    if (width > MAX_WIDTH || height > MAX_HEIGHT) {
      const aspectRatio = width / height;

      if (width > height) {
        // Ландшафтная ориентация
        width = Math.min(width, MAX_WIDTH);
        height = Math.round(width / aspectRatio);

        if (height > MAX_HEIGHT) {
          height = MAX_HEIGHT;
          width = Math.round(height * aspectRatio);
        }
      } else {
        // Портретная ориентация
        height = Math.min(height, MAX_HEIGHT);
        width = Math.round(height * aspectRatio);

        if (width > MAX_WIDTH) {
          width = MAX_WIDTH;
          height = Math.round(width / aspectRatio);
        }
      }
    }

    // Изменяем размер изображения
    const resizedBuffer = await sharp(sourcePath)
      .resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .toBuffer();

    // Базовое имя файла без расширения
    const baseName = path.basename(targetPath, ext);
    const targetDir = path.dirname(targetPath);

    // Создаем WebP версию
    const webpPath = path.join(targetDir, `${baseName}.webp`);
    await sharp(resizedBuffer)
      .webp({ quality: QUALITY })
      .toFile(webpPath);

    // Создаем оптимизированную версию в исходном формате
    if (ext === '.jpg' || ext === '.jpeg') {
      await sharp(resizedBuffer)
        .jpeg({ quality: QUALITY, mozjpeg: true })
        .toFile(targetPath);
    } else if (ext === '.png') {
      await sharp(resizedBuffer)
        .png({ quality: QUALITY, compressionLevel: 9 })
        .toFile(targetPath);
    } else {
      // Для других форматов просто сохраняем WebP
      fs.copyFileSync(webpPath, targetPath);
    }

    console.log(`✅ Изображение ${sourcePath} успешно оптимизировано`);
    console.log(`   Исходный размер: ${metadata.width}x${metadata.height}`);
    console.log(`   Новый размер: ${width}x${height}`);
    console.log(`   Создана WebP версия: ${webpPath}`);
  } catch (error) {
    console.error(`❌ Ошибка при оптимизации ${sourcePath}:`, error);
  }
}

// Запускаем обработку
console.log('Начинаем оптимизацию изображений...');
processDirectory(sourceDir, targetDir)
  .then(() => {
    console.log('Оптимизация изображений завершена!');
  })
  .catch((error) => {
    console.error('Ошибка при оптимизации изображений:', error);
  });
